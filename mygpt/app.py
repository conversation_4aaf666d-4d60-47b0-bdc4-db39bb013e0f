import traceback
import uuid
from urllib.parse import urljoin

import asyncio
import pydantic
import sentry_sdk
import tiktoken
from fastapi import FastAPI, Request
from fastapi.exception_handlers import (
    http_exception_handler,
    request_validation_exception_handler,
)
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.docs import (
    get_redoc_html,
    get_swagger_ui_html,
    get_swagger_ui_oauth2_redirect_html,
)
from fastapi.openapi.utils import get_openapi
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from loguru import logger
from loguru import logger as logging
from skywalking import agent, config
from starlette.exceptions import HTTPException as StarletteHTTPException
from tortoise.contrib.fastapi import register_tortoise

from mygpt import settings
from mygpt.agent_exp.core.prompt_manager import PromptManager
from mygpt.auth0.init import init_users
from mygpt.endpoints import api_router, router
from mygpt.endpoints.dataset import Stream, background_sync_task
from mygpt.endpoints.notification import notifier
from mygpt.enums import FileStorage
from mygpt.prompt_history import PromptCache
from mygpt.settings import (
    ENABLE_PHOENIX,
    RedisClient,
    CLONE_TASKS_PER_SERVER,
    ENABLE_BACKGROUND_LARK_SYNC,
)
from mygpt.startup_tasks import (
    poll_clone_task_streams,
)
from mygpt.util import trace
from mygpt.utils import (
    colored_info_red,
    colored_info_yellow,
)

if ENABLE_PHOENIX:
    import phoenix as px

    session = px.launch_app()
    from phoenix.trace.langchain import LangChainInstrumentor, OpenInferenceTracer

    tracer = OpenInferenceTracer()
    LangChainInstrumentor(tracer).instrument()

if settings.IS_USE_LOCAL_VLLM and settings.ENABLE_SENTRY:
    sentry_sdk.init(
        dsn="https://<EMAIL>/4507306422042624",
        traces_sample_rate=0.0,
        profiles_sample_rate=0.0,
        environment="on premises",
    )
else:
    if settings.ENABLE_SENTRY:
        sentry_sdk.init(
            dsn=settings.SENTRY_DSN,
            # dsn="https://<EMAIL>/4505589228634112",
            # Set traces_sample_rate to 1.0 to capture 100%
            # of transactions for performance monitoring.
            # We recommend adjusting this value in production.
            traces_sample_rate=0.0,
            environment=settings.SENTRY_ENVIRONMENT,
        )


def init_apm():
    if not settings.APM_BACKEND or not settings.APM_CURRENT_AGENT_NAME:
        logging.info("Connect to APM is disable")
        return

    config.init(
        agent_collector_backend_services=settings.APM_BACKEND,
        agent_protocol="grpc",
        agent_authentication="",
        agent_name=settings.APM_CURRENT_AGENT_NAME,
        agent_meter_reporter_active=False,
        agent_log_reporter_active=False,
        agent_logging_level="ERROR",
        agent_trace_ignore_path=settings.SW_AGENT_TRACE_IGNORE_PATH,
    )
    agent.start()

    logging.info("Connect to APM is enable")


# invoid dapr client invoke errors
# nest_asyncio.apply()

# Define OpenAPI specs for each document
app = FastAPI(
    docs_url=None,
    redoc_url=None,
    openapi_url=None,
    root_path=settings.ROOT_PATH,
)


@app.exception_handler(StarletteHTTPException)
async def custom_http_exception_handler(request, exc):
    logging.info(f"OMG! An HTTP exception!: {repr(exc)}")
    logging.debug(traceback.format_exc())
    return await http_exception_handler(request, exc)


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc):
    logging.error(
        f"OMG! The client sent invalid data!: {exc}, {traceback.format_exc()}"
    )
    logging.error(f"error: {exc.errors()}")
    logging.error(f"req body: {await request.body()}")
    return await request_validation_exception_handler(request, exc)


async def catch_exceptions_middleware(request: Request, call_next):
    try:
        return await call_next(request)
    except pydantic.error_wrappers.ValidationError as e:
        sentry_sdk.capture_exception(e)
        # logging.error(e)
        logging.error(traceback.format_exc())
        return JSONResponse(
            status_code=500,
            content={"message": str(e.args)},
        )
    except Exception as e:
        sentry_sdk.capture_exception(e)
        # logger.error(e)
        logging.error(traceback.format_exc())
        return JSONResponse(
            status_code=500,
            content={"message": str(e)},
        )


from starlette.middleware.sessions import SessionMiddleware

app.add_middleware(SessionMiddleware, secret_key="QWEDSAZXC")
app.middleware("http")(catch_exceptions_middleware)

app.include_router(router)
# Generate Swagger UI and ReDoc pages for each OpenAPI document
app.include_router(api_router)

origins = ["*"]

# CORSMiddleware should be the last middleware
# Error are not reported using CORS middleware
# https://github.com/encode/starlette/issues/1116
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# uploads file
if settings.FILE_STORAGE == FileStorage.LOCATION:
    import os

    if not os.path.exists("uploads"):
        os.makedirs("uploads")
    app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")


@app.middleware("http")
async def add_trace_id(request: Request, call_next):
    trace_id = request.headers.get("X-Request-ID") or str(uuid.uuid4())
    with trace.trace_id_context(trace_id):
        # 使用常规logger记录日志，补丁已经应用，会自动获取trace_id
        logger.info(f"处理请求，trace_id: {trace_id}")

        response = await call_next(request)
        response.headers["X-Request-ID"] = trace_id
        return response


@app.get("/sentry-debug")
async def trigger_error():
    division_by_zero = 1 / 0


@app.get("/docs-swagger", include_in_schema=False)
async def get_documentation(request: Request):
    root_path = request.scope.get("root_path") or "/"
    domain = settings.API_DOMAIN.rstrip('/')
    # 使用 urljoin 处理斜杠，避免出现多余的 //
    openapi_url = urljoin(f"{domain}{root_path}/", "openapi.json")
    oauth2_redirect_url = urljoin(root_path + "/", "docs-swagger/oauth2-redirect")
    return get_swagger_ui_html(
        openapi_url=openapi_url,
        oauth2_redirect_url=oauth2_redirect_url,
        title="Documentation",
    )


@app.get("/docs-swagger/oauth2-redirect", include_in_schema=False)
async def oauth2_redirect(request: Request):
    return get_swagger_ui_oauth2_redirect_html()


@app.get("/docs", include_in_schema=False)
async def get_documentation(request: Request):
    openapi_url = settings.API_DOMAIN.rstrip('/') + '/openapi-redoc.json'
    return get_redoc_html(
        openapi_url=openapi_url,
        title="GBase Documentation",
        redoc_favicon_url="https://gbase.ai/favicon.ico",
    )


@app.get("/openapi-redoc.json", include_in_schema=False)
async def get_internal_openapi_endpoint(request: Request):
    root_path = request.scope.get("root_path") or settings.API_DOMAIN
    openapi_dict = get_openapi(
        title="GBase",
        version="0.0.1",
        description="""# Introduce
This API document can help you understand GBase more deeply. 
You can train your own AI directly through the interface of this document.


##  Authentication

Home page: [https://gbase.ai](https://gbase.ai)

Huggingface: [https://huggingface.co/gptbase](https://huggingface.co/gptbase)

The GBase API uses API keys for authentication. Visit your API Keys page to retrieve the API key you'll use in your requests.

Remember that your API key is a secret! Do not share it with others or expose it in any client-side code (browsers, apps). 
Production requests must be routed through your own backend server where your API key can be securely loaded from an environment variable or key management service.

All API requests should include your API key in an Authorization HTTP header as follows:

Parameter name: Authorization

Value: Bearer [YOUR_API_KEY](https://admin.gbase.ai/keys)

```
Authorization: Bearer YOUR_API_KEY
```

## Use steps
1.Fill in your API keys.

2.You can create an AI by using either the API interface or the administrative backend. Once created, the AI will generate an ID which will be required for the subsequent steps.

3.Use the generated ID to call the file upload interface.

4.After uploading the file, you can use the question interface to inquire about it.
""",
        routes=api_router.routes,
        # routes=app.routes,
        servers=[{"url": root_path}],
    )
    security_schemes = openapi_dict["components"]["securitySchemes"]
    openapi_dict["components"]["securitySchemes"] = {
        "HTTPBearer": security_schemes["HTTPBearer"]
    }
    return openapi_dict


@app.get("/openapi.json", include_in_schema=False)
async def get_openapi_endpoint(request: Request):
    root_path = request.scope.get("root_path") or settings.API_DOMAIN
    openapi_dict = get_openapi(
        title="GBase",
        version="0.0.1",
        routes=router.routes,
        servers=[{"url": root_path}],
    )
    return openapi_dict


@app.get("/configs", summary="Get global configs")
async def get_configs():
    advanced_parser = [
        {"value": desc[0], "label": desc[1], "enabled": desc[2]}
        for desc in settings.ADVANCED_PARSER_DESC
    ]
    return {"advanced_parser": advanced_parser}


@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    # 在每个请求前执行一些操作
    cookie_name = "d_id"
    cookie_value = request.cookies.get(cookie_name, "")
    if not cookie_value:
        cookie_value = str(uuid.uuid4()).replace("-", "")
    else:
        # Convert to string if it's not already
        cookie_value = str(cookie_value)
    request.state.request_id = cookie_value
    response = await call_next(request)
    # 在每个请求后执行一些操作
    response.set_cookie(key=cookie_name, value=cookie_value)
    return response


register_tortoise(
    app,
    config=settings.TORTOISE_ORM,
    generate_schemas=False,
)


async def init_user_with_jwt():
    if settings.AUTHORIZATION_TYPE == "jwt":
        if not settings.DEFAULT_USER_EMAIL:
            logging.warning(
                colored_info_red("not found default user email, skip init user")
            )
            return
        default_emails = settings.DEFAULT_USER_EMAIL.split(",")
        default_passwords = (
            settings.DEFAULT_USER_PASSWORD.split(",")
            if settings.DEFAULT_USER_PASSWORD
            else None
        )

        await init_users(default_emails, default_passwords)
    else:
        # 非 jwt 模式下，不需要初始化用户
        logging.info(colored_info_yellow("not jwt mode, skip init user"))


@app.on_event("startup")
async def startup():
    init_apm()
    # 在服务启动时，加载tiktoken的编码
    tiktoken.get_encoding("cl100k_base")
    # setup prompt cache,use cache prompt
    if settings.DEBUG:
        await PromptCache.setup(True)
    else:
        await PromptCache.setup()

    from mygpt.train import train

    train.train_init()

    # 初始化trace_id并配置日志
    trace.init_trace_log(settings.LOG_PATH)


@app.on_event("startup")
async def handle_uncomplete_files() -> None:
    # signal.signal(signal.SIGINT, settings.stop_server)
    await asyncio.create_task(init_user_with_jwt())
    redis_client = RedisClient.get_client()
    channel = settings.RedisPubSub(redis_client)
    # 订阅webscoket消息
    asyncio.create_task(
        channel.subscribe(
            {
                "notification_client_login": notifier.handle_redis_message,
                settings.CHANNEL_STREAM_EVENT: Stream.receive_redis_message,
            }
        )
    )
    logging.info("startup continue")
    if not settings.STARTUP_CONTINUE:
        return
    lock_key = "startup_lock"
    acquired = await redis_client.set(lock_key, 1, nx=True, ex=60 * 3)
    if acquired:
        pass
        # logging.info("start handle_unprocessed_doc")
        # asyncio.create_task(handle_unprocessed_doc())
        # asyncio.create_task(handle_unfinished_html_dataset())
        # asyncio.create_task(handle_unfinished_file())

    # asyncio.create_task(delete_all_faq_embedding())


@app.on_event("startup")
async def init_agent() -> None:
    logging.info("init agent ")
    PromptManager.init_prompt()


from mygpt.grpc_server.server import serve_grpc, stop_grpc


@app.on_event("startup")
async def start_grpc_server():
    """启动gRPC服务器"""
    logging.info("start grpc server...")
    asyncio.create_task(serve_grpc())


@app.on_event("shutdown")
async def shutdown_grpc():
    """关闭gRPC服务器"""
    logging.info("shutdown grpc server...")
    await stop_grpc()


@app.get("/")
def read_root():
    return "Hello from GBase!"


@app.on_event("startup")
async def start_polling():
    for _ in range(CLONE_TASKS_PER_SERVER):
        asyncio.create_task(poll_clone_task_streams())
    # asyncio.create_task(poll_data_update_task_streams())


@app.on_event("startup")
async def start_sync():
    if ENABLE_BACKGROUND_LARK_SYNC:
        asyncio.create_task(background_sync_task())


if __name__ == "__main__":
    import uvicorn

    log_config = uvicorn.config.LOGGING_CONFIG
    # print(log_config["formatters"]["default"]["fmt"])
    log_config["formatters"]["access"][
        "fmt"
    ] = "%(asctime)s - %(levelprefix)s %(message)s"

    uvicorn.run(app, host="127.0.0.1", port=8000)
