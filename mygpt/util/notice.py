import logging
import os
import time
from typing import Dict, Tuple

from mygpt.send_email import send_admin_alert_email
from mygpt.settings import RedisClient

# 错误类型常量
ERROR_TYPE_AI_API = "error_ai_api"  # API 503错误，通常是API账号异常
ERROR_TYPE_VECTOR = "error_vector"  # 向量数据库连接错误
ERROR_TYPE_GENERAL = "error_general"  # 业务错误，不能恢复

# 错误类型匹配规则
ERROR_TYPE_PATTERNS = {
    ERROR_TYPE_AI_API: [
        "Error code: 401",
        "Error code: 503",
        "Error code: 429",
        "Error code: 529",
        "Service Unavailable",
        "Rate limit exceeded",
        "API quota exceeded",
    ],
    ERROR_TYPE_VECTOR: [
        "All connection attempts failed",
    ],
}

# 默认配置 (count, window(秒), cooldown(秒))
DEFAULT_THRESHOLD = (10, 60, 600)


def _parse_threshold_config(config_str: str) -> <PERSON><PERSON>[int, int, int]:
    """解析阈值配置字符串，格式：'count,window,cooldown'"""
    try:
        count, window, cooldown = map(int, config_str.strip().split(","))
        return count, window, cooldown
    except (ValueError, AttributeError):
        return DEFAULT_THRESHOLD


# 从环境变量读取每种错误类型的配置
ERROR_CONFIGS: Dict[str, Tuple[int, int, int]] = {
    ERROR_TYPE_AI_API: _parse_threshold_config(
        os.environ.get("NOTICE_AI_API_CONFIG", ",".join(map(str, DEFAULT_THRESHOLD)))
    ),
    ERROR_TYPE_VECTOR: _parse_threshold_config(
        os.environ.get("NOTICE_VECTOR_CONFIG", ",".join(map(str, DEFAULT_THRESHOLD)))
    ),
    ERROR_TYPE_GENERAL: DEFAULT_THRESHOLD,
}


def _get_redis_key(error_type: str, field: str) -> str:
    """生成Redis键名"""
    return f"error_notice:{error_type}:{field}"


def _get_error_type(error_msg: str) -> str:
    """根据错误消息确定错误类型"""
    error_msg = error_msg.lower()
    for error_type, patterns in ERROR_TYPE_PATTERNS.items():
        if any(pattern.lower() in error_msg for pattern in patterns):
            return error_type
    return ERROR_TYPE_GENERAL


async def notice(e: str):
    notice_list = os.environ.get("EMAIL_ADMIN_OUTGOING_NOTICE_LIST")
    if not notice_list:
        logging.error("EMAIL_ADMIN_OUTGOING_NOTICE_LIST 环境变量未设置")
        return

    try:
        error_msg = str(e)
        logging.info(f"收到错误消息: {error_msg}")

        # 使用新的错误类型判断逻辑
        error_type = _get_error_type(error_msg)
        logging.info(f"错误类型判断: {error_type}")

        current_time = int(time.time())
        threshold_count, time_window, cooldown = ERROR_CONFIGS[error_type]
        logging.info(
            f"当前配置 - 阈值: {threshold_count}, 时间窗口: {time_window}秒, 冷却时间: {cooldown}秒"
        )

        redis_client = RedisClient.get_client()

        # 获取Redis中的计数器状态
        count_key = _get_redis_key(error_type, "count")
        last_sent_key = _get_redis_key(error_type, "last_sent")
        first_error_key = _get_redis_key(error_type, "first_error")

        # 获取当前状态
        last_sent = int(await redis_client.get(last_sent_key) or 0)
        first_error = int(await redis_client.get(first_error_key) or 0)

        logging.info(f"Redis状态 - 上次发送: {last_sent}, 首次错误: {first_error}")

        # 检查是否需要开始新的错误计数周期
        if (
            current_time - last_sent > cooldown
            and current_time - first_error > time_window  # 超过冷却时间
        ):  # 超过时间窗口
            # 只有同时满足冷却时间和时间窗口条件才重置计数
            await redis_client.set(count_key, 1)
            await redis_client.set(first_error_key, current_time)
            current_count = 1
            logging.info("开始新的错误计数周期")
        else:
            # 在时间窗口内继续累加计数
            current_count = int(await redis_client.incr(count_key))
            if current_count == 1:  # 如果是第一次错误，记录时间
                await redis_client.set(first_error_key, current_time)

        logging.info(
            f"当前错误计数: {current_count}, 首次错误时间: {first_error}, 当前时间: {current_time}, 时间窗口: {time_window}"
        )

        # 检查是否需要发送邮件
        should_send = current_count >= threshold_count and (  # 达到阈值
            last_sent == 0 or current_time - last_sent > cooldown
        )  # 首次发送或超过冷却时间

        logging.info(
            f"是否需要发送邮件: {should_send}, 原因: "
            + f"计数={current_count}>={threshold_count}, "
            + f"冷却检查: last_sent={last_sent}, "
            + f"距离上次发送={current_time - last_sent}>{cooldown}"
        )

        if should_send:
            error_type_display = {
                ERROR_TYPE_AI_API: "API 503 429等 错误 (API账号/限流问题)",
                ERROR_TYPE_VECTOR: "向量数据库连接错误",
                ERROR_TYPE_GENERAL: "通用系统错误",
            }.get(error_type, "未分类错误")

            # 格式化错误信息
            error_details = (
                f"错误类型: {error_type_display}\n"
                f"错误次数: {current_count} (阈值: {threshold_count})\n"
                f"时间窗口: {time_window}秒\n"
                f"冷却时间: {cooldown}秒\n"
                f"原始错误: {error_msg}"
            )

            logging.info(f"准备发送邮件到: {notice_list}")
            try:
                send_admin_alert_email(
                    to_email=notice_list,
                    title=f"错误报警: {error_type_display}",
                    cause=error_details,
                )
            except Exception as e:
                logging.error(f"发送邮件失败: {str(e)}", exc_info=True)
                pass
            logging.info("邮件发送完成")

            # 更新发送时间并重置计数
            await redis_client.set(last_sent_key, current_time)
            await redis_client.set(count_key, 0)
            await redis_client.set(first_error_key, 0)
            logging.info("重置计数器状态")

    except Exception as ex:
        logging.error(f"发送邮件失败: {str(ex)}", exc_info=True)
        pass
