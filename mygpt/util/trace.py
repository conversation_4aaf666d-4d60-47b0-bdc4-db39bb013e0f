from loguru import logger
import uuid
import threading
import contextlib
import logging
import sys
from mygpt import settings

import contextvars
# 创建一个上下文变量
trace_id_var = contextvars.ContextVar('trace_id', default=None)

def get_trace_id():
    # 从上下文变量获取，如果不存在则创建
    trace_id = trace_id_var.get()
    if trace_id is None:
        trace_id = "inner-" + str(uuid.uuid4())
        trace_id_var.set(trace_id)
    return trace_id

def set_trace_id(trace_id=None):
    trace_id_var.set(trace_id or "inner-" + str(uuid.uuid4()))

@contextlib.contextmanager
def trace_id_context(trace_id=None):
    """上下文管理器，用于设置TraceID"""
    # original_trace_id = getattr(_thread_local, 'trace_id', None)
    original_trace_id = trace_id_var.get()
    set_trace_id(trace_id)
    try:
        yield
    finally:
        if original_trace_id is not None:
            set_trace_id(original_trace_id)
        else:
            trace_id_var.set(None)
            # delattr(_thread_local, 'trace_id')

class InterceptHandler(logging.Handler):
    def emit(self, record):
        # 获取对应的Loguru级别
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno
        
        # 找到调用origin
        frame, depth = sys._getframe(6), 6
        while frame and frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage()
        )

# 自定义format函数，确保每次日志记录都包含trace_id
def custom_format(record):
    # 确保record中包含trace_id
    if "trace_id" not in record["extra"]:
        record["extra"]["trace_id"] = get_trace_id()
    
    # 返回格式化后的日志字符串
    return "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {extra[trace_id]} | {name}:{function}:{line} - {message}\n"

def init_trace_log(log_file_path):
    """初始化trace_log日志配置"""
    # 确保每个logger.add调用都使用自定义format函数
    # 首先移除所有现有的处理器（包括默认的stderr处理器）
    logger.remove(handler_id=None)
    # 添加到stderr，确保基本日志显示
    log_level = 'DEBUG' if settings.DEBUG else 'INFO'
    logger.add(sys.stderr, level=log_level, format=custom_format)

    # 添加日志文件处理器
    if log_file_path:
        logger.info(f"LOG_PATH: {log_file_path}")
        # 获取日志轮转和保留设置，如果settings不可用则使用默认值
        rotation = getattr(settings, 'LOG_FILE_ROTATION', "1 day") if settings else "1 day"
        retention = getattr(settings, 'LOG_FILE_RETENTION', "7 days") if settings else "7 days"
        
        logger.add(
            log_file_path,
            format=custom_format,
            level=log_level,
            rotation=rotation,
            retention=retention,
            backtrace=True,
            diagnose=True
        )
    
    logging.basicConfig(handlers=[InterceptHandler()], level=logging.DEBUG if settings.DEBUG else logging.INFO, force=True)



