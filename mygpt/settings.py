import asyncio
import logging
import os
import secrets
import ssl
import string
from urllib.parse import quote, urlencode

import loguru
import redis.asyncio as redis
from dotenv import load_dotenv
from loguru import logger as _logging
from pydantic import BaseModel
from redis.exceptions import ConnectionError

from mygpt.enums import EMBEDDINGS_MODEL, OpenAIModel, VectorStorageType

ssl._create_default_https_context = ssl._create_unverified_context


def urlencode_for_db(s):
    return quote(str(s), safe="")


def transbool(v):
    return bool(v.lower() == "true")


on_premises_env = os.getenv("ON_PREMISES_ENV")
prod_env = os.getenv("PROD_ENV")
test_env = os.getenv("TEST_ENV")
local_env = os.getenv("LOCAL_ENV")
on_premises_re_embed_env = os.getenv("ON_PREMISES_RE_EMBED_ENV")

if on_premises_re_embed_env:
    loguru.logger.info(f"ON_PREMISES_RE_EMBED_ENV: {on_premises_re_embed_env}")
    current_file_path = os.path.abspath(__file__)
    parent_dir = os.path.dirname(current_file_path)
    grandparent_dir = os.path.dirname(parent_dir)
    # 构建 .on-premises.env 文件的完整路径
    env_path = os.path.join(grandparent_dir, ".on-premises-re-embed.env")
    # 加载 .on-premises.env 文件中的环境变量
    load_dotenv(dotenv_path=env_path, override=True)
elif on_premises_env:
    loguru.logger.info(f"ON_PREMISES: {on_premises_env}")
    current_file_path = os.path.abspath(__file__)
    parent_dir = os.path.dirname(current_file_path)
    grandparent_dir = os.path.dirname(parent_dir)
    # 构建 .on-premises.env 文件的完整路径
    env_path = os.path.join(grandparent_dir, ".on-premises.env")
    # 加载 .on-premises.env 文件中的环境变量
    load_dotenv(dotenv_path=env_path, override=True)
elif prod_env:
    loguru.logger.info(f"PROD_ENV: {prod_env}")
    current_file_path = os.path.abspath(__file__)
    parent_dir = os.path.dirname(current_file_path)
    grandparent_dir = os.path.dirname(parent_dir)
    env_path = os.path.join(grandparent_dir, ".prod.env")
    load_dotenv(dotenv_path=env_path, override=True)
elif test_env:
    loguru.logger.info(f"TEST_ENV: {test_env}")
    current_file_path = os.path.abspath(__file__)
    parent_dir = os.path.dirname(current_file_path)
    grandparent_dir = os.path.dirname(parent_dir)
    env_path = os.path.join(grandparent_dir, ".test.env")
    load_dotenv(dotenv_path=env_path, override=True)
elif local_env:
    loguru.logger.info(f"local_env: {local_env}")
    current_file_path = os.path.abspath(__file__)
    parent_dir = os.path.dirname(current_file_path)
    grandparent_dir = os.path.dirname(parent_dir)
    env_path = os.path.join(grandparent_dir, ".local.env")
    load_dotenv(dotenv_path=env_path, override=True)
else:
    load_dotenv(override=True)
# os.environ['DAPR_API_METHOD_INVOCATION_PROTOCOL'] = 'GRPC'
# you should set environment DAPR_API_TOKEN for auth dapr client
# such: export DAPR_API_TOKEN=xxxxxxxx
# the value of token you should ask the manager

DEBUG = transbool(os.getenv("DEBUG", "False"))

server_env = os.getenv("ENV")
_logging.info(f"ENV: {server_env}")
_logging.info(f"DEBUG: {DEBUG}")

IS_DEV = server_env == "dev"
IS_LOCAL = server_env == "local"

# logconfig
LOG_PATH = os.getenv("LOG_PATH", None)
# LOG_PATH = "logs/app.log"
LOG_FILE_ROTATION = os.getenv("LOG_FILE_ROTATION", "0:00")
LOG_FILE_RETENTION = os.getenv("LOG_FILE_RETENTION", "15 days")

# user system autho config. see: https://auth0.com/docs/api/authentication#authentication-methods
AUTH0_DOMAIN = os.getenv("AUTH0_DOMAIN", "dev-w1oz7a1bpi7nbnl2.us.auth0.com")
AUTH0_API_AUDIENCE = os.getenv("AUTH0_API_AUDIENCE", "https://mygpt.felo.me")
AUTH0_CLIENT_ID = os.getenv("AUTH0_CLIENT_ID", "")
AUTH0_CLIENT_SECRET = os.getenv(
    "AUTH0_CLIENT_SECRET",
    "",
)
AUTH0_REDIRECT_URI = os.getenv(
    "AUTH0_REDIRECT_URI", "http://localhost:8000/docs/oauth2-redirect"
)
AUTH0_LOGOUT_REDIRECT_URI = os.getenv(
    "AUTH0_LOGOUT_REDIRECT_URI", "http://localhost:8000/docs/oauth2-redirect"
)

AUTH0_ADMIN_CLIENT_ID = os.getenv("AUTH0_ADMIN_CLIENT_ID", "")

AUTH0_ADMIN_CLIENT_SRCRET = os.getenv(
    "AUTH0_ADMIN_CLIENT_SRCRET",
    "",
)
AUTH0_ADMIN_API_AUDIENCE = os.getenv(
    "AUTH0_ADMIN_API_AUDIENCE", "https://dev-w1oz7a1bpi7nbnl2.us.auth0.com/api/v2/"
)

# oauth2 config for login&logout
AUTHORIZATION_URL_QS = urlencode({"audience": AUTH0_API_AUDIENCE})
AUTHORIZATION_URL = os.getenv(
    "AUTHORIZATION_URL", f"https://{AUTH0_DOMAIN}/authorize?{AUTHORIZATION_URL_QS}"
)
LOGOUT_URL = f"https://{AUTH0_DOMAIN}/v2/logout?client_id={AUTH0_CLIENT_ID}&returnTo={AUTH0_LOGOUT_REDIRECT_URI}"

# local jwt config
JWT_HEADER_KID = os.getenv("JWT_HEADER_KID", "gbase.ai")

AUTHORIZATION_TYPE = os.getenv("AUTHORIZATION_TYPE", "auth0")

# default user
DEFAULT_USER_EMAIL = os.getenv("DEFAULT_USER_EMAIL", "<EMAIL>")
# default random password
DEFAULT_USER_PASSWORD = os.getenv("DEFAULT_USER_PASSWORD", "")
# if remote client, place use grpc address
# else use None
DAPR_CLIENT_ADDRESS = os.getenv("DAPR_CLIENT_ADDRESS", "************:30095")
DAPR_APP_ID_AI = os.getenv("DAPR_APP_ID_AI", "felo-ai.default")

# Database settings
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", "5432")
DB_USER = os.getenv("DB_USER", "circleo")
DB_PASSWORD = os.getenv("DB_PASSWORD", "circleo")
DB_NAME = os.getenv("DB_NAME", "mygpt")
DB_CHARSET = os.getenv("DB_CHARSET", "utf8")
DB_URL = "postgres://{username}:{pwd}@{host}:{port}/{dbname}".format(
    username=urlencode_for_db(DB_USER),
    pwd=urlencode_for_db(DB_PASSWORD),
    host=urlencode_for_db(DB_HOST),
    port=urlencode_for_db(DB_PORT),
    dbname=urlencode_for_db(DB_NAME),
)

# TortoiseORM settings
TORTOISE_ORM = {
    "connections": {
        "default": {
            "engine": "tortoise.backends.asyncpg",
            "credentials": {
                "database": DB_NAME,
                "host": DB_HOST,
                "password": DB_PASSWORD,
                "port": DB_PORT,
                "user": DB_USER,
                "minsize": 10,
                "maxsize": 30,
                "max_inactive_connection_lifetime": 60,
            },
        }
    },
    "apps": {
        "models": {
            "models": [
                "mygpt.models",
                "aerich.models",
            ],
            "default_connection": "default",
        },
    },
}

print("DEBUG: {}")
if DEBUG:
    print(f"TORTOISE_ORM: {TORTOISE_ORM}")


# AWS
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID", "")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY", "")

AWS_S3_REGION = os.getenv("AWS_S3_REGION", "ap-northeast-1")
AWS_S3_BUCKET_NAME = os.getenv("AWS_S3_BUCKET_NAME", "prd-mygpt")

FILE_STORAGE = os.getenv("FILE_STORAGE", "s3")


def api_key() -> str:
    alphabet = string.ascii_letters + string.digits
    # 自定义字符集，包括大小写字母、数字和下划线、波浪线
    token = "".join(secrets.choice(alphabet) for _ in range(48))
    return f"ak-{token}"


PINECONE_API_KEY = os.getenv("PINECONE_API_KEY", None)
PINECONE_ENVIRONMENT = os.getenv("PINECONE_ENVIRONMENT", None)

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
CLAUDE_API_KEY = os.getenv("CLAUDE_API_KEY", "")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "")


def mask_key(key: str):
    return f"[{key[:9]}...{key[-6:]}]" if key else "[None]"


masked_openai_key = mask_key(OPENAI_API_KEY)
masked_claude_key = mask_key(CLAUDE_API_KEY)

loguru.logger.info(
    f"OPENAI_API KEY: {masked_openai_key}; CLAUDE_API KEY: {masked_claude_key}"
)

AES_SECRETS = b"mysecretpassword"


STARTUP_CONTINUE = transbool(os.getenv("STARTUP_CONTINUE", "True"))

# deprecated 不再显示指定代理，改为使用环境变量HTTPS_PROXY
LOCAL_PROXY = os.getenv("LOCAL_PROXY", None)

CONCURRENT_INTENT_DETECT = transbool(os.getenv("CONCURRENT_INTENT_DETECT", "False"))

OPENAI_JUST_AZURE = transbool(os.getenv("OPENAI_JUST_AZURE", "False"))

# LLM
REFERENCE_USE_LOCAL_LLM = transbool(os.getenv("REFERENCE_USE_LOCAL_LLM", "False"))
QUESTION_USE_LOCAL_LLM = transbool(os.getenv("QUESTION_USE_LOCAL_LLM", "False"))
LLM_URL = os.getenv("LLM_URL", "")

if DEBUG:
    logging.basicConfig(level=logging.DEBUG)
DOMAIN = os.getenv("DOMAIN", None)
API_DOMAIN = os.getenv("API_DOMAIN", "https://api.gbase.ai")

DEFAULT_CHECK_COMMAND = "which"
WINDOWS_CHECK_COMMAND = "where"
TESSERACT_DATA_PATH_VAR = "TESSDATA_PREFIX"

VALID_IMAGE_EXTENSIONS = [".jpg", ".jpeg", ".gif", ".png", ".tga", ".tif", ".bmp"]
PDF_OCR_SUPPORT_LANG = ["eng", "jpn", "chi_sim"]

CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/0")
CELERY_BACKEND_URL = os.getenv("CELERY_BACKEND_URL", "redis://localhost:6379/0")

# Redis
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
REDIS_DB = os.getenv("REDIS_DB", "0")
REDIS_URL_WITH_DB = f"{REDIS_URL}/{REDIS_DB}"

# stripe
STRIPE_API_KEY = os.getenv("STRIPE_API_KEY", "")

UNLIMITED_VERSION = transbool(os.getenv("UNLIMITED_VERSION", "False"))

OPENSEARCH_URL = os.getenv("OPENSEARCH_URL", None)

FAQS_PINECONE_API_KEY = os.getenv("FAQS_PINECONE_API_KEY", None)
FAQS_PINECONE_ENVIRONMENT = os.getenv("FAQS_PINECONE_ENVIRONMENT", None)

TRANSFORMERS_URL = os.getenv("TRANSFORMERS_URL", None)

FAQS_STATUS = transbool(os.getenv("FAQS_STATUS", "False"))

# website crawler
# vincent.li: 废弃WEBSITE_CRAWLER_URL, CRAWLER配置统一在gptparser上设置
# WEBSITE_CRAWLER_URL = os.getenv("WEBSITE_CRAWLER_URL", None)

# parser file service
PARSER_URL = os.getenv("PARSER_URL", None)
PARSER_TOKEN = os.getenv("PARSER_TOKEN", None)
PARSER_URLS = [x.strip() for x in os.getenv("PARSER_URLS", "").split(",") if x.strip()]
PARSER_TOKENS = [
    x.strip() for x in os.getenv("PARSER_TOKENS", "").split(",") if x.strip()
]
_logging.info(f"PARSER_URLS: {PARSER_URLS}, PARSER_TOKENS: {PARSER_TOKENS}")
if len(PARSER_URLS) != len(PARSER_TOKENS):
    _logging.error("PARSER_URLS and PARSER_TOKENS should have the same length")
    raise ValueError("PARSER_URLS and PARSER_TOKENS should have the same length")


QUALITY_ASSESSMENT_URL = os.getenv("QUALITY_ASSESSMENT_URL", None)

# vector storage
DEFAULT_VECTOR_STORAGE = VectorStorageType(
    os.getenv("DEFAULT_VECTOR_STORAGE", "qdrant_one_collection")
)
DEFAULT_FAQ_VECTOR_STORAGE = VectorStorageType(
    os.getenv("DEFAULT_FAQ_VECTOR_STORAGE", "qdrant_one_collection")
)
VECTOR_STORAGE_QDRANT_URL = os.getenv(
    "VECTOR_STORAGE_QDRANT_URL", "http://localhost:6333"
)

VECTOR_STORAGE_QDRANT_HOST = os.getenv("VECTOR_STORAGE_QDRANT_HOST", "localhost")

VECTOR_STORAGE_QDRANT_GRPC_PORT = os.getenv("VECTOR_STORAGE_QDRANT_GRPC_PORT", "6334")
VECTOR_STORAGE_QDRANT_HTTP_PORT = os.getenv("VECTOR_STORAGE_QDRANT_HTTP_PORT", "6333")

QDRANT_HTTP = os.getenv("QDRANT_HTTP", "false").lower() == "true"

# defualt embeddings_model
DEFAULT_EMBEDDINGS_MODEL = EMBEDDINGS_MODEL(
    os.getenv("DEFAULT_EMBEDDINGS_MODEL", "openai")
)

# TODO: The 'openai.proxy' option isn't read in the client API. You will need to pass it when you instantiate the client, e.g. 'OpenAI(proxy={"https": os.getenv("https_proxy")})'
# openai.proxy = {"https": os.getenv("https_proxy")}
# print(f"openai.proxy: {openai.proxy}")

COHERE_API_KEY = os.getenv("COHERE_API_KEY")
if not COHERE_API_KEY:
    _logging.warning("COHERE_API_KEY is not set")

ENABLE_SENTRY = os.getenv("ENABLE_SENTRY", "false").lower() == "true"
_logging.info(f"ENABLE_SENTRY: {ENABLE_SENTRY}")

SENTRY_DSN = os.getenv(
    "SENTRY_DSN",
    "https://<EMAIL>/4506040652070912",
)

SENTRY_ENVIRONMENT = os.getenv(
    "SENTRY_ENVIRONMENT", "development" if IS_DEV else "production"
)

ENABLE_TIMED_EXCEL_TEST = (
    os.getenv("ENABLE_TIMED_EXCEL_TEST", "false").lower() == "true"
)
_logging.info(f"ENABLE_TIMED_EXCEL_TEST: {ENABLE_TIMED_EXCEL_TEST}")

FINAL_QUESTION_MAX_CONVERSATION_COUNT = 3

# apm setting
APM_BACKEND = os.getenv("APM_BACKEND", None)
APM_CURRENT_AGENT_NAME = os.getenv("APM_CURRENT_AGENT_NAME", None)
# SW_AGENT_TRACE_IGNORE_PATH = os.getenv("SW_AGENT_TRACE_IGNORE_PATH", None)
SW_AGENT_TRACE_IGNORE_PATH = (
    # "/, /v1/spans, /notification/**"
    ""
)

# statistics
STATISTICS_KEY = os.getenv("STATISTICS_KEY", None)

FUNCTION_CALL_AUTH_URL = os.getenv("FUNCTION_CALL_AUTH_URL", None)
FUNCTION_CALL_AUTH_USERNAME = os.getenv("FUNCTION_CALL_AUTH_USERNAME", None)
FUNCTION_CALL_AUTH_PASSWORD = os.getenv(
    "FUNCTION_CALL_AUTH_PASSWORD", "U8Tzit9YdLpF5rMU3ZWX1g=="
)

MAIL_SERVER = os.getenv("MAIL_SERVER", "email-smtp.ap-northeast-1.amazonaws.com")
MAIL_PORT = os.getenv("MAIL_PORT", "587")
MAIL_USERNAME = os.getenv("MAIL_USERNAME", "")
MAIL_PASSWORD = os.getenv("MAIL_PASSWORD", "")
MAIL_FORM = os.getenv("MAIL_FORM", "")

ADMIN_DOMAIN = os.getenv("ADMIN_DOMAIN", "https://admin-dev.gbase.ai")

# local embedding
USE_LOCAL_EMBEDDING = transbool(os.getenv("USE_LOCAL_EMBEDDING", "False"))
LOCAL_EMBEDDING_TOKENIZER_ENDPOINT = os.getenv("LOCAL_EMBEDDING_TOKENIZER_ENDPOINT", "")
LOCAL_EMBEDDING_PROXY = os.getenv("LOCAL_EMBEDDING_PROXY", None)
LOCAL_EMBEDDING_KEY = os.getenv("LOCAL_EMBEDDING_KEY", None)
LOCAL_EMBEDDING_VECTOR_SIZE = os.getenv("LOCAL_EMBEDDING_VECTOR_SIZE", None)
LOCAL_EMBEDDING_MODEL_NAME = os.getenv("LOCAL_EMBEDDING_MODEL_NAME", None)

IS_USE_LOCAL_VLLM = os.getenv("IS_USE_LOCAL_VLLM", "false").lower() == "true"
LOCAL_LLM_MODEL = os.getenv("LOCAL_LLM_MODEL", "")

LOCAL_VLLM_URL = os.getenv("LOCAL_VLLM_URL", "http://34.146.198.179:9009/v1")
LOCAL_VLLM_MODEL = os.getenv("LOCAL_VLLM_MODEL", "rag")

CUSTOM_RERANK_ENDPOINT = os.getenv("CUSTOM_RERANK_ENDPOINT", "")

CUSTOM_RERANK_API_KEY = os.getenv("CUSTOM_RERANK_API_KEY", "")


class RedisClient:
    client: redis.Redis = None

    @classmethod
    def get_client(cls) -> redis.Redis:
        if cls.client is None:
            cls.client = redis.Redis.from_url(
                REDIS_URL_WITH_DB,
                decode_responses=True,
            )
        return cls.client

    def __init__(self):
        self.client = self.get_client()

    async def close(self):
        await self.client.close()


if not REDIS_URL:
    _logging.warning("REDIS_URL is not set")

AZURE_MODEL = {}


class AuzreModelConfig(BaseModel):
    deployment_name: str
    version: str
    endpoints: str
    identity_key: str


def init_azure_model():
    azure_model_version = os.getenv("AZURE_MODEL_VERSION", None)
    azure_model_endpoints = os.getenv("AZURE_MODEL_ENDPOINTS", None)
    azure_model_identity_key = os.getenv("AZURE_MODEL_IDENTITY_KEY", None)
    # 从环境变量中获取azure_model_xxx_deployment_name的key与值，注意：xxx是一个可变的模型变量
    for k, v in OpenAIModel.__members__.items():
        config = {"deployment_name": v}
        deplyment_key = f"AZURE_MODEL_{k}_DEPLOYMENT_NAME"
        if not os.getenv(deplyment_key):
            # 兼容一下老的配置
            if k == OpenAIModel.GPT_4_TURBO_PREVIEW.name:
                k = OpenAIModel.GPT_4_1106_PREVIEW.name
                deplyment_key = f"AZURE_MODEL_{k}_DEPLOYMENT_NAME"
            elif k == OpenAIModel.GPT_35_TURBO.name:
                k = OpenAIModel.GPT_35_TURBO_16K.name
                deplyment_key = f"AZURE_MODEL_{k}_DEPLOYMENT_NAME"

        version_key = f"AZURE_MODEL_{k}_VERSION"
        endpoints_key = f"AZURE_MODEL_{k}_ENDPOINTS"
        identity_key = f"AZURE_MODEL_{k}_IDENTITY_KEY"
        config["deployment_name"] = os.getenv(deplyment_key)
        if not config["deployment_name"]:
            continue
        config["version"] = os.getenv(version_key, azure_model_version)
        config["endpoints"] = os.getenv(endpoints_key, azure_model_endpoints)
        config["identity_key"] = os.getenv(identity_key, azure_model_identity_key)
        AZURE_MODEL[v.value] = AuzreModelConfig(**config)
        print_msg = f"""Azure Model {v}\ndeployment_name:{config["deployment_name"]}\nendpoints:{config['endpoints']}\nversion:{config['version']}"""
        _logging.info(print_msg)


init_azure_model()


def check_azure_model():
    if not OPENAI_JUST_AZURE and not CONCURRENT_INTENT_DETECT:
        # no need to check
        return
    items = OpenAIModel.__members__.items()
    for k, v in items:
        if v == OpenAIModel.CLAUDE_35_SONNET or v == OpenAIModel.GEMINI_15_PRO_EXP_0801:
            continue
        if v.value not in AZURE_MODEL:
            if OPENAI_JUST_AZURE:
                _logging.error(f"Azure Model {v} is not set")
                raise ValueError(
                    f"OPENAI_JUST_AZURE is ture but Azure Model {v} is not set"
                )
            if CONCURRENT_INTENT_DETECT:
                _logging.warning(f"Azure Model {v} is not set")
                raise ValueError(
                    f"CONCURRENT_INTENT_DETECT is ture but Azure Model {v} is not set"
                )


# 检查是否设置了Azure Model
check_azure_model()


def validate_environments():
    not_allow_empty_values = []
    for v in not_allow_empty_values:
        if not os.environ.get(v):
            _logging.error(f"Environment {v} is not set")
            raise ValueError(f"Environment {v} is not set")


# 指定GPT_4_TURBO_PREVIEW的模型，如果使用最新模型，默认值置空或使用gpt-4-turbo-preview
GPT_4_TURBO_PREVIEW = os.getenv("GPT_4_TURBO_PREVIEW", "gpt-4-1106-preview").lower()

# 指定GPT_35_TURBO_16K的模型。如果使用最新模型，默认值置空或使用gpt-3.5-turbo
GPT_35_TURBO = os.getenv("GPT_35_TURBO", "gpt-3.5-turbo-1106").lower()

validate_environments()

SERVER_RUNNING = True

# def stop_server(*args):
#     global SERVER_RUNNING
#     _logging.info("receive stop signal, stop the server")
#     SERVER_RUNNING = False


CHANNEL_STREAM_EVENT = "stream_event"


class RedisPubSub:
    def __init__(self, redis_client: redis.Redis):
        self.funcs = {}
        self.channel = None
        self.client = redis_client

    async def reader(self, channel: redis.client.PubSub):
        while True:
            message = await channel.get_message(ignore_subscribe_messages=True)
            if message is not None:
                try:
                    func = self.funcs[message["channel"]]
                    if func:
                        await func(message["data"])
                except Exception as e:
                    _logging.error(f"Error: {e}")
                    _logging.error(f"Message: {message}")
            else:
                await asyncio.sleep(0.1)

    async def subscribe(self, funcs: dict):
        self.funcs = funcs
        wait_time = 1
        while True:
            try:
                async with self.client.pubsub() as pubsub:
                    names = [name for name in self.funcs.keys()]
                    await pubsub.subscribe(*names)
                    future = asyncio.create_task(self.reader(pubsub))
                    await future
            except ConnectionError as e:
                _logging.error(f"ConnectionError: {e}")
                await asyncio.sleep(wait_time)
                wait_time = min(wait_time * 2, 60)

    async def publish(self, name, message):
        await self.client.publish(name, message)


ENABLE_PHOENIX = os.getenv("ENABLE_PHOENIX", "false").lower() == "true"

GOOGLE_CUSTOM_SEARCH_API_KEY = os.getenv("GOOGLE_CUSTOM_SEARCH_API_KEY", "")

ROOT_PATH = os.getenv("ROOT_PATH", None)  # 项目根目录,用于服务代理下的接口文档

PROJECT_PATH = os.path.dirname(os.path.abspath(__file__))

CLONE_TASKS_PER_SERVER = int(os.getenv("CLONE_TASKS_PER_SERVER", 3))

LLM_CONTEXT_LENGTH = int(os.getenv("LLM_CONTEXT_LENGTH", 0))

LLM_TIMEOUT = int(os.getenv("LLM_TIMEOUT", 120))

CUSTOM_RERANK_MODEL = os.getenv(
    "CUSTOM_RERANK_MODEL", "hotchpotch/japanese-bge-reranker-v2-m3-v1"
)
logging.info(
    f"CUSTOM_RERANK_MODEL: {CUSTOM_RERANK_MODEL}, CUSTOM_RERANK_ENDPOINT: {CUSTOM_RERANK_ENDPOINT}, CUSTOM_RERANK_API_KEY: {CUSTOM_RERANK_API_KEY}"
)

GEMINI_EXP = os.getenv("GEMINI_EXP", "false").lower() == "true"
FILE_LEARNING_TASKS_PER_SERVER = int(os.getenv("FILE_LEARNING_TASKS_PER_SERVER", 3))
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
if IS_USE_LOCAL_VLLM:
    MAX_FILE_SIZE = 400 * 1024 * 1024
FILE_LEARNING_TASKS_PER_SERVER_ADV = int(
    os.getenv("FILE_LEARNING_TASKS_PER_SERVER_ADV", 1)
)

# Maximum number of files allowed for upload
MAX_FILES_ALLOWED = 100


HF_TOKEN = os.getenv("HF_TOKEN", "")
LOCAL_LLM_MODEL = os.getenv("LOCAL_LLM_MODEL", "")

# 高级文档解析设置, 默认全部高级文档解析关闭，除非显式Enabled打开
ADVANCED_PARSER_DESC = os.getenv(
    "ADVANCED_PARSER_DESC", "3,Gemini,true:1,Gpt-4o,true:2,Claude,true:4,GBase-VL,true"
)

VECTOR_FILE_DEFAULT_LEARN_TYPE = int(
    os.getenv("VECTOR_FILE_DEFAULT_LEARN_TYPE", "100")
)  # 0: Aspose, 100: Markitdown

ADVANCED_PARSER_DESC = [
    desc.strip().split(",") for desc in ADVANCED_PARSER_DESC.strip().split(":")
]
for desc in ADVANCED_PARSER_DESC:
    assert (
        len(desc) == 3
        and int(desc[0]) > 0
        and (desc[2].lower() == "true" or desc[2].lower() == "false")
    )
    desc[0] = int(desc[0])
    desc[2] = desc[2].lower() == "true"


AZURE_STT_TOKEN_SUBSCRIPTION_KEY = os.getenv("AZURE_STT_TOKEN_SUBSCRIPTION_KEY", "")

FILE_PROCESSING_CONCURRENCY = int(os.getenv("FILE_PROCESSING_CONCURRENCY", 1))

DINAMIC_SECTION_EXTRACT = (
    os.getenv("DINAMIC_SECTION_EXTRACT", "false").lower() == "true"
)
DINAMIC_SECTION_RETRIEVE = (
    os.getenv("DINAMIC_SECTION_RETRIEVE", "false").lower() == "true"
)

ENABLE_BACKGROUND_LARK_SYNC = (
    os.getenv("ENABLE_BACKGROUND_LARK_SYNC", "true").lower() == "true"
)

GRPC_MAX_MESSAGE_LENGTH = 100 * 1024 * 1024
GRPC_SERVER_PORT = int(os.getenv("GRPC_SERVER_PORT", "50051"))
GRPC_MAX_WORKERS = int(os.getenv("GRPC_MAX_WORKERS", "100"))


NEW_TRAIN_ENABLED = transbool(os.getenv("NEW_TRAIN_ENABLED", "true"))
NEW_TRAIN_DISTRIBUTE = transbool(os.getenv("NEW_TRAIN_DISTRIBUTE", "true"))
NEW_TRAIN_CONCURRENT = int(os.getenv("NEW_TRAIN_CONCURRENT", 4))
NEW_TRAIN_SYNC_TIMEOUT = int(os.getenv("NEW_TRAIN_SYNC_TIMEOUT", 3600))
NEW_TRAIN_CHECK_INTERVAL = int(os.getenv("NEW_TRAIN_CHECK_INTERVAL", 3600))
NEW_TRAIN_AUTH = None
NEW_TRAIN_VERSION_NUM = 0
NEW_TRAIN_BULK_COUNT = int(os.getenv("NEW_TRAIN_BULK_COUNT", 30))

# 默认启用，正式环境部署时，按实际情况配置
QUOTA_CHECK = transbool(os.getenv("QUOTA_CHECK", "true"))
