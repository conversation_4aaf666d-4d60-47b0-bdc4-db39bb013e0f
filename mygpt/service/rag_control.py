import asyncio
import copy
import datetime
import time
from typing import Optional, List

from fastapi.responses import StreamingResponse
from langchain.schema import HumanMessage, AIMessage, SystemMessage
from loguru import logger as logging
from skywalking.decorators import trace

from mygpt import settings

# from mygpt.endpoints.faqs import faq_context_search, question_faq
from mygpt.service.chat.faq_service import question_faq, faq_context_search
from mygpt.service.chat.function_call_service import (
    send_function_call,
    function_call_api,
)
from mygpt.enums import AIConfigType, UserIntent, MESSAGE_COMES_FROM, OpenAIModel
from mygpt.error import (
    sentry_sdk_capture_exception,
)
from mygpt.service.chat.faq_service import question_faq_answer
from mygpt.models import Robot, SessionMessage, FaqResource
from mygpt.openai_utils import (
    user_intent_detect,
    chat_acreate,
    get_chat_history_turbo,
    get_final_chat_context,
)
from mygpt.prompt import (
    ANSWER_ASK_ABOUT_BOT_SYSTEM_PROMPT,
    gpt_4_turbo_fallback_with_general_knowledge_final_question_prompts,
    fallback_with_general_knowledge_final_question_prompts,
    fallback_with_general_knowledge_final_question_prompts_with_image,
    gpt_4_turbo_fallback_with_general_knowledge_with_image_final_question_prompts,
    turbo_system_content,
    turbo_user_prompt,
    gpt_4_turbo_final_question_prompts,
    gpt_4_turbo_final_question_prompts_with_image,
    get_question_dictionary_prompt,
    get_talking_style,
    get_unknown_text,
    get_question_environments,
    get_extra_goal,
)
from mygpt.schemata import QuestionIn, QuestionOut, FinalQuestionParams
from mygpt.service.chat.doc_service import search_question_context
from mygpt.service.chat.web_service import async_web_search
from mygpt.settings import FINAL_QUESTION_MAX_CONVERSATION_COUNT, FAQS_STATUS
from mygpt.utils import (
    convert_language_code,
    replace_md_uuid_images,
    num_tokens_for_langchain_messages,
    check_content_is_rich_text,
)


def get_final_question_prompt_template(
    response_language: Optional[str] = None,
    openai_model: OpenAIModel = None,
    chat_with_image=False,
):
    if openai_model.startswith("gpt-3.5"):
        system_prompt = turbo_system_content
        user_prompt = turbo_user_prompt
    else:
        system_prompt = gpt_4_turbo_final_question_prompts["system"]
        user_prompt = gpt_4_turbo_final_question_prompts["user"]

    if chat_with_image:
        system_prompt = gpt_4_turbo_final_question_prompts_with_image["system"]
        user_prompt = gpt_4_turbo_final_question_prompts_with_image["user"]

    user_prompt = user_prompt if response_language else "{question} {environments}"

    return system_prompt, user_prompt


def get_fallback_final_prompt(
    openai_model: OpenAIModel = None,
    chat_with_image=False,
):
    if openai_model == OpenAIModel.GPT_4_TURBO_PREVIEW:
        system_prompt = (
            gpt_4_turbo_fallback_with_general_knowledge_final_question_prompts["system"]
        )
        user_prompt = (
            gpt_4_turbo_fallback_with_general_knowledge_final_question_prompts["user"]
        )
    else:
        system_prompt = fallback_with_general_knowledge_final_question_prompts["system"]
        user_prompt = fallback_with_general_knowledge_final_question_prompts["user"]
    if chat_with_image:
        system_prompt = (
            fallback_with_general_knowledge_final_question_prompts_with_image["system"]
        )
        user_prompt = fallback_with_general_knowledge_final_question_prompts_with_image[
            "user"
        ]
    if openai_model == OpenAIModel.GPT_4_TURBO_PREVIEW and chat_with_image:
        system_prompt = gpt_4_turbo_fallback_with_general_knowledge_with_image_final_question_prompts[
            "system"
        ]
        user_prompt = gpt_4_turbo_fallback_with_general_knowledge_with_image_final_question_prompts[
            "user"
        ]
    return system_prompt, user_prompt


@trace()
async def prepare_messages(
    question: QuestionIn,
    embeddings: list,
    params: FinalQuestionParams,
    chat_with_image=False,
):
    if not params.chat_history:
        params.chat_history = []
    if params.fallback_to_chatgpt:
        system_prompt, user_prompt = get_fallback_final_prompt(
            params.openai_model,
            chat_with_image,
        )
    else:
        system_prompt, user_prompt = get_final_question_prompt_template(
            params.response_language,
            params.openai_model,
            chat_with_image,
        )
    dict_prompt = get_question_dictionary_prompt(params.dictionaries)
    messages = [
        SystemMessage(
            content=system_prompt.format(
                bot_name=params.bot_name,
                subject_name=params.display_subject_name,
                context=f"{dict_prompt}",
                response_language=params.response_language,
                talking_style=get_talking_style(params.talking_style),
                unknown_text=get_unknown_text(params.unknown_text),
                current_time=datetime.datetime.now(datetime.timezone.utc).strftime(
                    "%Y-%m-%d %H:%M:%S %Z"
                ),
            )
        )
    ]
    messages.extend(params.chat_history)
    messages.append(
        HumanMessage(
            content=user_prompt.format(
                question=question.question,
                environments=get_question_environments(question.metadata),
                response_language=params.response_language,
                extra_goal=get_extra_goal(question.prompt_custom_goal),
                talking_style=get_talking_style(params.talking_style),
            )
        )
    )
    # system + history + user message + ai max_token
    message_token = num_tokens_for_langchain_messages(messages)
    calculate_tokens = message_token + question.max_tokens

    context, total_tokens, reference_list, resources = await get_final_chat_context(
        params.openai_model,
        embeddings,
        calculate_tokens,
        params.llm_provider,
        question.with_images,
    )

    # 去除faq干扰的
    new_reference_list = []
    if reference_list:
        for item in reference_list:
            if item["source"] != "GPTBASE_FAQ":
                new_reference_list.append(item)

    reference_list = new_reference_list

    context = f"{dict_prompt}{context}"

    include_image, include_table = check_content_is_rich_text([context])
    use_4_turbo = include_image or include_table
    # 如果包含表格，并且模型不是gpt-4-turbo，并且是fallback_to_chatgpt，需要更换新的prompt内容
    table_recall = (
        include_table
        and params.openai_model != OpenAIModel.GPT_4_TURBO_PREVIEW
        and params.fallback_to_chatgpt
    )
    if (
        params.openai_model == OpenAIModel.GPT_35_TURBO
        or params.openai_model == OpenAIModel.GPT_4
    ) and use_4_turbo:
        params.openai_model = OpenAIModel.GPT_4_TURBO_PREVIEW
    # 如果包含图片，并且非chat_with_image，需要更换新的prompt内容
    image_recall = include_image and not chat_with_image
    # 在上下文匹配到表格或者图片，需要更换新的prompt内容
    if (table_recall or image_recall) and not chat_with_image:
        return await prepare_messages(
            question,
            embeddings,
            params,
            chat_with_image=True,
        )

    logging.info(
        f"include_image:{include_image}, include_table: {include_table}, \
            chat_with_image: {chat_with_image}, fallback_to_chatgpt: {params.fallback_to_chatgpt}"
    )

    # logging.info(f"reference_list: {reference_list}")

    messages[0].content = system_prompt.format(
        bot_name=params.bot_name,
        subject_name=params.display_subject_name,
        context=context,
        response_language=params.response_language,
        talking_style=get_talking_style(params.talking_style),
        unknown_text=get_unknown_text(params.unknown_text),
        current_time=datetime.datetime.now(datetime.timezone.utc).strftime(
            "%Y-%m-%d %H:%M:%S %Z"
        ),
    )
    return messages, resources, reference_list, total_tokens, params.openai_model


@trace()
async def create_final_chat_question(
    ai_obj: Robot,
    question: QuestionIn,
    question_record_obj: SessionMessage,
    embeddings: list,
    response_language: Optional[str] = None,
    chat_history: Optional[list] = None,
    event: Optional[asyncio.Event] = None,
    recommend_list: List[dict] = None,
    faq_child_node_list: List[dict] = None,
):
    use_gpt_4 = ai_obj.get_config(AIConfigType.FINAL_QUESTION_USE_GPT4) == "True"
    model = ai_obj.get_config(AIConfigType.OPENAI_MODEL)
    logging.info(f"create_final_chat_question, model: {model}, ai_obj: {ai_obj}")
    if not model:
        # old gpt-4 conf
        model = "gpt_4" if use_gpt_4 else OpenAIModel.default().value
    openai_model = OpenAIModel.to_model(model)

    logging.info(f"bot: {ai_obj.id}, final openai_model: {openai_model}")
    fallback_to_chatgpt = (
        ai_obj.get_config(AIConfigType.FALLBACK_TO_CHATGPT).lower() == "true"
    )
    llm_provider = ai_obj.get_config(AIConfigType.LLM_PROVIDER)
    talking_style = ai_obj.get_config(AIConfigType.TALKING_STYLE)
    chat_temperature = None
    try:
        chat_temperature = ai_obj.get_config(AIConfigType.CHAT_TEMPREATURE)
        temperature = float(chat_temperature) if chat_temperature else 0
    except Exception as e:
        logging.warning(f"fail to get chat_temperature:{chat_temperature}, error: {e}")
        temperature = 0
    unknown_text = ai_obj.get_config(AIConfigType.UNKNOWN_TEXT)

    # 如果没有传入 chat_history，则从数据库中获取
    chat_history = await add_missing_history(chat_history, question)

    params = FinalQuestionParams(
        bot_name=ai_obj.name,
        display_subject_name=ai_obj.display_subject_name,
        unknown_text=unknown_text,
        response_language=response_language,
        chat_history=chat_history,
        openai_model=openai_model,
        llm_provider=llm_provider,
        fallback_to_chatgpt=fallback_to_chatgpt,
        dictionaries=ai_obj.dictionaries(question.question),
        talking_style=talking_style,
    )

    (
        messages,
        resources,
        reference_list,
        total_tokens,
        openai_model,
    ) = await prepare_messages(
        question,
        embeddings,
        params,
    )
    question_record_obj.reference_list = reference_list
    question_record_obj.total_tokens = total_tokens
    question_record_obj.model = openai_model
    logging.info(
        f"use final_question:{repr(messages[-1].content)} ask ai: {ai_obj.id}, final_question_use_gpt_4: {use_gpt_4}, openai_model: {openai_model} in messages: {repr(messages)}"
    )

    none_content_reference_list = []
    for reference in question_record_obj.reference_list:
        none_content_reference_list.append({**reference, "content": ""})
    resp = await chat_acreate(
        origin_question=question.question,
        messages=messages,
        temperature=temperature,
        max_tokens=question.max_tokens,
        message_id=question.message_id,
        session_id=question.session_id,
        stream=question.stream,
        reference_list=none_content_reference_list,
        resources=resources,
        format=question.format,
        final_question_use_gpt_4=use_gpt_4,
        openai_model=openai_model,
        event=event,
        llm_provider=llm_provider,
        recommend_list=recommend_list,
        faq_child_node_list=faq_child_node_list,
    )

    question_record_obj.contexts = [message.content for message in messages]
    question_record_obj.comes_from = MESSAGE_COMES_FROM.CHUNK.value
    if question.stream and question.debug_with_messages:
        new_messages = copy.deepcopy(messages)
        image_resources = resources.get("image", {}) if resources else None
        if image_resources:
            for message in new_messages:
                message.content = replace_md_uuid_images(
                    message.content, image_resources
                )
        question_record_obj.answer = resp.content
    return resp, question_record_obj, messages


async def add_missing_history(chat_history: list, question: QuestionIn) -> list:
    """从数据库中取出并添加历史记录"""
    # 如果已有历史记录，直接返回
    if chat_history:
        return chat_history
        
    # 获取最近的聊天记录，过滤掉无法回答的问题
    history = await SessionMessage.filter(
        session_id=question.session_id,
        llm_can_answer__not=False
    ).order_by("-created_at")
    
    # 如果没有历史记录，返回空列表
    if not history:
        return []
        
    # 移除第一项（当前问题）
    history = history[1:]
    
    # 收集最近的5个有效问答对
    valid_pairs = []
    pair_count = 0
    
    for session in history:
        # 跳过没有回答的会话
        if not session.answer or not session.question:
            continue
            
        # 收集有效的问答对
        valid_pairs.append(session)
        
        # 计数并检查是否达到限制
        pair_count += 1
        if pair_count >= 5:
            break
    
    # 反转顺序，使其按时间先后排列（从旧到新）
    valid_pairs.reverse()
    
    # 构建聊天历史
    chat_history = []
    for session in valid_pairs:
        chat_history.append(HumanMessage(content=session.question))
        chat_history.append(AIMessage(content=session.answer))
    
    return chat_history


@trace()
async def answer_ask_about_bot(
    ai_obj: Robot,
    question: QuestionIn,
    event: asyncio.Event,
    response_language: str | None,
):
    logging.info(f"[answer_ask_about_bot] - question: {question}")
    model = ai_obj.get_config(AIConfigType.OPENAI_MODEL)
    chatgpt_mode = ai_obj.get_config(AIConfigType.FALLBACK_TO_CHATGPT).lower() == "true"
    logging.info(f"[answer_ask_about_bot] - model: {model}, chatgpt_mode: {chatgpt_mode}")

    if not model:
        model = OpenAIModel.default()
    if settings.IS_USE_LOCAL_VLLM:
        model = settings.LOCAL_VLLM_MODEL
    logging.info(f"[answer_ask_about_bot] - final base model: {model}")
    time1 = time.monotonic()
    history = (
        await SessionMessage.filter(session_id=question.session_id)
        .order_by("-created_at")
        .limit(3)
    )
    logging.info(
        f"history len: {len(history)}, {history}, time: {time.monotonic() - time1}"
    )

    # Remove the first item(current question), and reverse the order
    history = history[1:][::-1]

    description_text = (
        f'The business scope of the company is "{ai_obj.discrible}". '
        if ai_obj.discrible
        else ""
    )
    logging.info(f"[answer_ask_about_bot] - description_text: {description_text}")

    what_can_you_do_str = (
        f"I can answer questions about based on my knowledge base, if I can not find the answer in the knowledge base, I will use my own knowledge to help you. "
        if chatgpt_mode
        else f"I can answer your questions about solely based on my knowledge base."
    )
    logging.info(f"[answer_ask_about_bot] - what_can_you_do_str: {what_can_you_do_str}")

    description_text += f"""
If user asks like "what can you do?", you should say literally: 
"{what_can_you_do_str}"
"""

    messages = [
        SystemMessage(
            content=ANSWER_ASK_ABOUT_BOT_SYSTEM_PROMPT.format(
                bot_name=ai_obj.name,
                subject_name=ai_obj.display_subject_name,
                description_text=description_text,
                model=model,
            )
        )
    ]

    # Append history messages
    for session in history:
        messages.append(HumanMessage(content=session.question or ""))
        messages.append(AIMessage(content=session.answer or ""))
    question_text = f"{question.question}"
    if response_language:
        question_text = f"{question_text}\n\n(Please respond in {response_language})"
    messages.append(HumanMessage(content=question_text))
    logging.info(
        f"answer_ask_about_bot messages: {messages}, question: {question}, response_language: {response_language}"
    )
    # openai_model = OpenAIModel.to_model(model)
    openai_model = OpenAIModel.GPT_4_OMNI
    resp = await chat_acreate(
        origin_question=question.question,
        messages=messages,
        max_tokens=question.max_tokens,
        session_id=question.session_id,
        message_id=question.message_id,
        stream=question.stream,
        format=question.format,
        event=event,
        user_intent=UserIntent.ASK_INFO_ABOUT_BOT,
        openai_model=openai_model
    )

    if question.stream:
        return StreamingResponse(
            resp,
            media_type="text/event-stream",
        )
    elif question.debug_with_messages:
        return QuestionOut(answer=resp.content, messages=messages)
    else:
        return QuestionOut(answer=resp.content)


@trace()
async def analyze_question(
    question: QuestionIn,
    ai_obj: Robot,
    chat_history_str: str = None,
    user_id: str = None,
):
    ai_lang = ai_obj.get_config(AIConfigType.SOURCE_LANG)
    if not ai_lang:
        ai_lang = "en"
    resp = None
    user_intent = UserIntent.ASK_INFO_ABOUT_COMPANY.value
    query_key_words = question.question
    response_language = ai_lang
    message_tokens = 0

    start_time = time.time()
    try:
        resp, message_tokens = await user_intent_detect(
            session_id=question.session_id,
            question=question.question,
            chat_history_str=chat_history_str,
            dictionaries=ai_obj.dictionaries(question.question),
            ai_language=ai_lang,
            user_id=user_id,
            ai_id=str(ai_obj.id),
        )
    except Exception as e:
        logging.error(f"fail to explain question in error: {e}, response json:{resp}")
        sentry_sdk_capture_exception(e)
        user_intent = UserIntent.ASK_INFO_ABOUT_COMPANY.value
        query_key_words = question.question
        response_language = ai_lang
    finally:
        logging.info(
            f"【{question.message_id}】Handle question cost time: {time.time() - start_time} seconds"
        )

    if not resp:
        logging.warning(
            f"【{question.message_id}】error to detect user intent, ai:{ai_obj.id} question:{question}"
        )
    else:
        response_language = resp.get("question_language", None)
        user_intent = resp.get("user_intent", None)
        if not user_intent:
            logging.warning(
                f"【{question.message_id}】user_intent is None, ai:{ai_obj.id} question:{question}"
            )
        elif user_intent == UserIntent.ASK_INFO_ABOUT_BOT.value:
            pass
        else:
            if user_intent != UserIntent.ASK_INFO_ABOUT_COMPANY.value:
                logging.warning(f"unknown user intent, response:{resp}")
                user_intent = UserIntent.ASK_INFO_ABOUT_COMPANY.value

            query_key_words = resp.get("query_key_words", None)
            if not query_key_words:
                query_key_words = question.question

    logging.info(
        f"【{question.message_id}】ai:{ai_obj.id} detect question:{question.question}, ai_lang:{ai_lang}, response_language:{response_language}, query_key_words:{query_key_words}, resp: {resp}"
    )
    return (
        user_intent,
        response_language,
        query_key_words,
        message_tokens,
    )


async def faq_handler(
    faq_search_res,
    question_in: QuestionIn,
    response_language: str,
    message_tokens,
    user_id: str,
    ai_id: str,
    chat_history,
    question_record_obj,
    event,
):
    (
        rs_type,
        faq_obj,
        property_info_list,
        new_query,
        original_faq_obj_list,
        opensearch_faq_obj_list,
    ) = faq_search_res
    if rs_type == "no_faqs":
        resp = None
    else:
        if property_info_list and len(property_info_list) > 0:
            question_faq_answer_start_time = time.time()
            resp, max_tokens = await question_faq_answer(
                rs_type,
                faq_obj,
                property_info_list,
                question_in,
                new_query,
                response_language,
                message_tokens,
                ai_id,
                user_id,
                event,
                chat_history,
            )
            logging.info(
                f"【FAQ_ASK】question_faq_answer time {time.time() - question_faq_answer_start_time}"
            )
            question_record_obj.total_tokens = max_tokens
        else:
            question_faq_start_time = time.time()
            (
                resp,
                max_tokens,
                reference_list,
                answer_faq_id,
                _recommend_list,
                _faq_child_node_list,
            ) = await question_faq(
                ai_id,
                new_query,
                question_in,
                response_language,
                message_tokens,
                faq_obj,
                original_faq_obj_list,
                opensearch_faq_obj_list,
                user_id=user_id,
                event=event,
            )
            recommend_list = _recommend_list
            faq_child_node_list = _faq_child_node_list
            logging.info(
                f"【FAQ_ASK】question_faq time {time.time() - question_faq_start_time}"
            )
            if not resp:
                faq_resouces = await FaqResource.filter(faq_id=answer_faq_id)
                if faq_resouces:
                    vector_collection_filter = [
                        {
                            "vector_file_id": recource.vector_file_id,
                            "page_numbers": recource.page_numbers,
                        }
                        for recource in faq_resouces
                    ]
                    # embeddings = await search_question_context(
                    #     ai_obj=ai_obj,
                    #     response_language=response_language,
                    #     query=question.question,
                    #     query_key_words=query_key_words,
                    #     vector_collection_filter=vector_collection_filter,
                    # )
                    logging.info(
                        f"【FAQ_ASK】question_faq use vector_collection_filter: {vector_collection_filter}"
                    )

            question_record_obj.reference_list = reference_list
            question_record_obj.total_tokens = max_tokens
            question_record_obj.comes_from = MESSAGE_COMES_FROM.FAQ.value
            question_record_obj.faq_id = answer_faq_id
    return resp


async def answer_rag_bot(
    user_id: str,
    ai_obj: Robot,
    question_in: QuestionIn,
    question_record_obj,
    chat_history_str,
    event,
):
    # 1. 解析用户意图, 使用的语言, 用于向量检索的关键词
    question_results = (
        await analyze_question(  # 解析用户意图, 使用的语言, 用于向量检索的关键词
            question_in, ai_obj, chat_history_str, None
        )
    )
    (
        user_intent,  # 用户输入的分类识别, 相当于意图检测, 是闲聊还是任务. (ABOUT_BOT, ABOUT_COMPANY)
        response_language,  # 检测用户使用的语言, 回复时按照用户使用的语言进行回复
        query_key_words,  # 当前问句中的关键信息 (相当于实体抽取)
        message_tokens,  # 执行当前任务消耗的token数量
    ) = question_results
    response_language = convert_language_code(response_language)
    logging.info(
        f"【answer_rag_bot】user_intent: {user_intent}, response_language: {response_language}, query_key_words: {query_key_words}"
    )
    # 2. 判断用户意图, 如果是关于机器人的问题, 则直接返回机器人的信息
    if (
        user_intent == UserIntent.ASK_INFO_ABOUT_BOT.value
        and question_in.recommend_faq_id is None
    ):
        return await answer_ask_about_bot(ai_obj, question_in, event, response_language)
    # 3. 如果用户意图是专业知识问答, 则进行RAG问答
    # 3.1 文档向量检索
    logging.info(f"【answer_rag_bot】doc vector search start")
    coroutine_datasets_search = search_question_context(
        ai_obj, question_in.question, response_language, query_key_words=query_key_words
    )
    # 3.2 FAQ向量检索
    logging.info(f"【answer_rag_bot】faq vector search start")
    coroutine_faq_search = faq_context_search(
        ai_id=str(ai_obj.id),
        response_language=response_language,
        question=question_in.question,
        query_key_words_in_ai_language=query_key_words,
    )
    # 3.3 FC请求验证
    logging.info(f"【answer_rag_bot】fc function call start")
    coroutine_function_call_api = send_function_call(
        f"{question_in.question}",
        ai_obj.id,
        None,
        query_key_words,
        question_in.session_id,
    )
    # 3.4 WEB内容检索
    logging.info(f"【answer_rag_bot】web search start")
    coroutine_web_search = async_web_search(
        ai_obj=ai_obj, query_key_words=query_key_words, question=question_in.question
    )
    # 3.5 历史记录检索
    logging.info(f"【answer_rag_bot】chat history search start")
    coroutine_chat_history = get_chat_history_turbo(
        question_in.session_id, FINAL_QUESTION_MAX_CONVERSATION_COUNT
    )
    # 3.6 并发执行以上5个任务
    logging.info(f"【answer_rag_bot】start asyncio.gather")
    tasks = [
        coroutine_datasets_search,
        coroutine_function_call_api,
        coroutine_web_search,
        coroutine_chat_history,
    ]
    logging.info(f"【answer_rag_bot】question_in.use_faq: {question_in.use_faq}, FAQS_STATUS: {FAQS_STATUS}")
    if question_in.use_faq and FAQS_STATUS:
        tasks.append(coroutine_faq_search)
    results = await asyncio.gather(*tasks)
    use_faq = False
    if question_in.use_faq and FAQS_STATUS:
        use_faq = True
        (
            embeddings_res,
            fc_search_res,
            web_search_res,
            chat_history_res,
            faq_search_res,
        ) = results
    else:
        embeddings_res, fc_search_res, web_search_res, chat_history_res = results
        faq_search_res = None
    embeddings = embeddings_res
    embeddings.extend(web_search_res)
    embeddings.sort(key=lambda x: x.score, reverse=True)
    # 4. 并行计算结果选择
    function_call_sta, gpt_response, operations, function_call_infos = fc_search_res
    # 4.1 优先使用Function Call的结果
    resp = None
    history_conversation_count = 0
    recommend_list = []
    faq_child_node_list = []
    if function_call_sta:
        resp, max_tokens, send_info = await function_call_api(
            gpt_response,
            operations,
            function_call_infos,
            None,
            query_key_words,
            response_language,
            question_in,
            event=event,
        )
        if resp is not None:
            question_record_obj.comes_from = MESSAGE_COMES_FROM.FUNCTION_CALL.value
    # 4.2 如果Function Call没有结果, 再优先匹配FAQ的结果
    chat_history = None
    if resp is None and use_faq:
        # 处理FAQ的结果
        resp = await faq_handler(
            faq_search_res,
            question_in,
            response_language,
            message_tokens,
            user_id,
            str(ai_obj.id),
            chat_history_str,
            question_record_obj,
            event,
        )
    else:
        chat_history, history_conversation_count = chat_history_res
    # 4.3 FC和FAQ都没有结果, 则使用文档向量检索的结果
    question_record_obj.question_metadata = {
        "message_id": str(question_in.message_id),
        "user_intent": user_intent,
        "response_language": response_language,
        "query_key_words": query_key_words,
    }
    messages = None
    if not resp:
        if question_in.with_images is None:
            question_in.with_images = (
                ai_obj.get_config(AIConfigType.ENABLE_IMAGES).lower() == "true"
            )

        final_chat_question_embeddings = list()
        if (
            ai_obj.get_config(AIConfigType.RECOMMEND).lower() == ""
            or ai_obj.get_config(AIConfigType.RECOMMEND).lower() == "false"
        ):
            final_chat_question_embeddings = embeddings
        elif (
            ai_obj.get_config(AIConfigType.FAQ_SEARCH_ANSWER_WITH_EMBEDDING).lower()
            == "true"
        ):
            final_chat_question_embeddings = [
                e for e in embeddings if "faq_id" in e.metadata
            ]

        resp, question_record_obj, messages = await create_final_chat_question(
            ai_obj,
            question_in,
            question_record_obj,
            final_chat_question_embeddings,
            response_language,
            chat_history,
            event,
            recommend_list=recommend_list,
            faq_child_node_list=faq_child_node_list,
        )
        question_record_obj.history_conversation_count = history_conversation_count
    if question_in.stream:
        return StreamingResponse(
            resp,
            media_type="text/event-stream",
        )
    elif question_in.debug_with_messages:
        return QuestionOut(answer=resp.content, messages=messages)
    else:
        question_record_obj.answer = resp.content
        return QuestionOut(answer=resp.content)
