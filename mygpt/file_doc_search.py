import asyncio
import copy
import heapq
import math
import re
import time
import traceback
from typing import Dict, List, Optional

from loguru import logger as logging
from skywalking.decorators import trace

from mygpt import settings
from mygpt.core.cohere_utils import AsyncClientSession
from mygpt.core.embedding import EmbeddingFactory
from mygpt.core.retriever import Retriever
from mygpt.core.utils import get_collection_name
from mygpt.core.vector_storage import VectorStorageFactory, process_point
from mygpt.enums import (
    DATASET_STATUS,
    EMBEDDINGS_MODEL,
    AIConfigType,
    OpenAIModel,
    VectorStorageType,
)
from mygpt.error import sentry_sdk_capture_exception
from mygpt.models import Dataset, Robot, EmbeddingParams, Faqs
from mygpt.openai_utils import get_llm_context_length
from mygpt.opensearch import (
    OpenSearchClient,
    get_open_search_client,
    fetch_from_opensearch,
)
from mygpt.schemata import ContextQueryOptions, Embeddings, SearchOption, FaqsOutSearch
from mygpt.settings import COHERE_API_KEY
from mygpt.utils import (
    get_analyzer,
    num_tokens_from_string,
)


def apply_boost(
    combined_results: dict,
    vector_boost_level: float = 0.5,
    bm25_boost_level: float = 0.5,
):
    boost_results = {}
    for key, value in combined_results.items():
        if len(value) == 1:
            boost_results[key] = value[0]
        else:
            boost_results[key] = (
                value[0] * vector_boost_level + value[1] * bm25_boost_level
            )
    return boost_results


@trace()
async def dataset_bm25_search(
    os_client: OpenSearchClient,
    search_options: list[SearchOption],
    use_query_key_words: bool = False,
):
    tasks = []
    collection_names = []
    for search_option in search_options:
        if not search_option.analyzer:
            continue
        # text_size = int(search_option.text_size)
        text_size = 500
        if use_query_key_words:
            # text_size = search_option.text_size - int(search_option.text_size)
            tasks.append(
                os_client.async_clauses_query_search(
                    search_option.collection_name,
                    search_option.analyzer,
                    search_option.query_key_words,
                    text_size,
                )
            )
        else:
            tasks.append(
                os_client.async_query_search(
                    search_option.collection_name,
                    search_option.analyzer,
                    search_option.query,
                    text_size,
                    get_short_chunk=False,
                )
            )
        collection_names.append(search_option.collection_name)
    results = await asyncio.gather(*tasks)

    return dict(zip(collection_names, results))


@trace()
async def qa_bm25_search(
    os_client: OpenSearchClient,
    search_options: list[SearchOption],
    min_score: float = 0.8,
    use_query_key_words: bool = True,
):
    tasks = []
    collection_names = []
    for search_option in search_options:
        if not search_option.analyzer:
            continue
        # text_size = int(search_option.text_size)
        text_size = 500
        if use_query_key_words:
            # text_size = search_option.text_size - int(search_option.text_size)
            tasks.append(
                os_client.async_clauses_query_search(
                    search_option.collection_name,
                    search_option.analyzer,
                    search_option.query_key_words,
                    text_size,
                    True,
                )
            )
        else:
            tasks.append(
                os_client.async_query_search(
                    search_option.collection_name,
                    search_option.analyzer,
                    search_option.query,
                    text_size,
                    get_short_chunk=False,
                )
            )
        collection_names.append(search_option.collection_name)

    try:
        results = await asyncio.gather(*tasks)
        result_dic = dict(zip(collection_names, results))

        faq_recalls = []
        for record in results:
            if not record:  # Check if record is None due to a previous error
                continue
            faq_records = record["hits"]["hits"]
            for faq_record in faq_records:
                score = faq_record["_score"]
                if score >= min_score:
                    faq_recalls.append(faq_record)

        if not faq_recalls:
            return []
        # Fetch faqs_obj in batches
        faqs_ids = []
        for item in faq_recalls:
            if "faq_id" in item["_source"]["metadata"]:
                faqs_ids.append(item["_source"]["metadata"]["faq_id"])

        faqs_objs = await Faqs.filter(
            dataset_id__in=collection_names, id__in=faqs_ids, deleted_at__isnull=True
        ).prefetch_related("config", "resources", "resources__vector_file")

        out_list = []
        for item in faq_recalls:
            if item["_score"] < min_score:
                break
            if "faq_id" in item["_source"]["metadata"]:
                faqs_obj = next(
                    (
                        faqs
                        for faqs in faqs_objs
                        if str(faqs.id) == item["_source"]["metadata"]["faq_id"]
                    ),
                    None,
                )
                if faqs_obj is not None:
                    temporary_score = item["_score"]
                    out = await FaqsOutSearch.from_tortoise_orm(faqs_obj)
                    out.score = temporary_score
                    out_list.append(out)

        logging.info(
            f"qa_bm25_search: collection_names: {collection_names}, results: {results}, faqs_ids: {faqs_ids}, out_list: {out_list}"
        )
        return out_list

    except Exception as e:
        logging.error(f"Error processing qa_bm25_search recall: {e}")
        sentry_sdk_capture_exception(e)
        return []


@trace()
async def index_query_limit(
    dataset_ids: list[str],
    embedding_params: EmbeddingParams,
):
    vector_storage = VectorStorageFactory.get_instance()
    collection_name = get_collection_name(
        embedding_params.model_name,
        embedding_params.dimensions,
    )
    filters = {"must": [{"key": "group_id", "match": {"any": dataset_ids}}]}
    total_vector_size = await vector_storage.index_count(
        collection_name=collection_name, filters=filters, adapt_old_ds=True
    )
    index_query_limit = (
        min(380, max(75, int(total_vector_size * 0.06))) if total_vector_size > 0 else 0
    )
    logging.info(
        f"total_vector_size: {total_vector_size}, index_query_limit: {index_query_limit}, collection_name: {collection_name}"
    )
    return index_query_limit


async def index_query(
    search_options: list[SearchOption],
    query_embeddings: list[float],
    embedding_params: EmbeddingParams,
    with_short_chunks: bool = True,
    score_threshold: float = 0.4,
) -> dict[str, list[Embeddings]]:
    start_time = time.time()
    collection_names = [i.collection_name for i in search_options]
    limit = await index_query_limit(collection_names, embedding_params)
    if limit == 0:
        return {id: [] for id in collection_names}
    retriever = Retriever.get_instance()
    response = await retriever.retrieve(
        dataset_ids=collection_names,
        query="",
        score_threshold=score_threshold,
        limit=limit,
        embeddings=query_embeddings,
        embedding_model_name=embedding_params.model_name,
        additional_filters=search_options[0].vector_collection_filter,
        **embedding_params.dict(),
    )
    if search_options[0].query_faq_answer_with_embeddings:
        faq_answer_response = await retriever.retrieve_faq_answer(
            dataset_ids=collection_names,
            embeddings=query_embeddings,
            embedding_model_name=embedding_params.model_name,
            dimensions=embedding_params.dimensions,
        )
        response.extend(faq_answer_response)
    rs = {id: [] for id in collection_names}
    for item in response:
        rs[item.dataset_id].append(item)
    logging.info(
        f"index_query spend time:{time.time() - start_time}, collection_names:{collection_names}"
    )
    return rs


@trace()
async def vector_query_multi_dataset(
    search_options: list[SearchOption],
    embedding_params: EmbeddingParams,
    query_embeddings: list[list[float]],
) -> dict[str, list[Embeddings]]:
    task = []
    for idx, query_embedding in enumerate(query_embeddings):
        task.append(
            index_query(
                search_options=search_options,
                query_embeddings=query_embedding,
                embedding_params=embedding_params,
                with_short_chunks=True,
                score_threshold=0.15,
            )
        )
    start = time.time()
    vector_search_results = await asyncio.gather(*task)
    logging.info(f"multi_dataset_result spend time:{time.time() - start}")

    final_results = {}

    # 假设vector_search_results中只有两个元素
    if len(vector_search_results) != 2:
        raise ValueError("Expected exactly two results from vector_search_results")

    query_results, keywords_results = vector_search_results

    # 将两个dict结果合并，根据key取并集，注意key的值可能为空
    all_keys = set(keywords_results.keys()) | set(query_results.keys())
    for key in all_keys:
        values = keywords_results.get(key, []) + query_results.get(key, [])
        # 将values根据id进行去重，并取其中score更高的结果
        values_dict = {}
        for value in values:
            if value.id not in values_dict or value.score > values_dict[value.id].score:
                values_dict[value.id] = value
        values = list(values_dict.values())
        for value in values:
            value.metadata["embedding_score"] = value.score
        values = sorted(values, key=lambda x: x.score, reverse=True)
        final_results[key] = values
    return final_results


class RRFItem:
    doc_id: str
    chunk_index: int

    def __init__(self, doc_id: str, chunk_index: int):
        self.doc_id = doc_id
        self.chunk_index = chunk_index


# rrf: https://plg.uwaterloo.ca/~gvcormac/cormacksigir09-rrf.pdf
# input: lists of ranked ids
# returns: { _id: score }
@trace()
def large_chunks_rrf(initial_ranked_lists: list[list[str]]) -> dict[str, float]:
    # use K = 60 as in the paper
    K = 60
    weight_vec = 1.2
    rrf_score_dict: dict[str, float] = {}
    for l_idx, results in enumerate(initial_ranked_lists):
        if l_idx == 0:
            weight = weight_vec
        else:
            weight = 1
        for rank, _id in enumerate(results, start=1):
            if _id not in rrf_score_dict:
                rrf_score_dict[_id] = 0
            rrf_score_dict[_id] += 1 / (K + rank) * weight

    # Sort by score in descending order and return a sorted dictionary
    new_dict = {
        k: v
        for k, v in sorted(
            rrf_score_dict.items(), key=lambda item: item[1], reverse=True
        )
    }
    return new_dict


class OrderedSet:
    def __init__(self, iterable=None):
        self.data = dict.fromkeys(iterable or [])

    def add(self, item):
        self.data[item] = None

    def __contains__(self, item):
        return item in self.data

    def __iter__(self):
        return iter(self.data)


# vectors_hits and bm25_results must be sorted by score
@trace()
async def single_dataset_result(
    vector_hits: list[Embeddings],
    bm25_results: list[Embeddings],
    embedding_params: EmbeddingParams,
) -> list[Embeddings]:
    # indexes of all parent chunks from small chunk of embedding search
    parent_indexes = []
    vec_id_embed_score_dict: Dict[str, float] = {}
    # map from large chunk vector's id to the vector, save small chunks' parent_id to parent_indexes
    vec_id_embed_dict: Dict[str, Embeddings] = {}
    for hit in vector_hits:
        hit_id = hit.id
        parent_index = hit.metadata.get("parent_index")
        # if metadata of an embedding has parent_index, then it is a short_chunk
        if parent_index:
            if vec_id_embed_dict.get(parent_index):
                continue
            vec_id_embed_score_dict[parent_index] = max(
                vec_id_embed_score_dict.get(parent_index, 0), hit.score
            )
            vec_id_embed_dict[parent_index] = hit
            parent_indexes.append(parent_index)
        else:
            vec_id_embed_dict[hit_id] = hit
            vec_id_embed_score_dict[hit_id] = max(
                vec_id_embed_score_dict.get(hit_id, 0), hit.score
            )
    # get os results
    if not bm25_results:
        bm25_hits = []
    else:
        bm25_hits = bm25_results["hits"]["hits"]

    ranked_embedding_chunk_ids = list(vec_id_embed_dict.keys())
    ranked_bm25_chunk_ids = [v["_id"] for v in bm25_hits]

    rrf_score_dict = large_chunks_rrf(
        [ranked_embedding_chunk_ids, ranked_bm25_chunk_ids]
    )

    # logging.info(f"single_dataset_result rrf_score_dict: {rrf_score_dict}, ranked_embedding_chunk_ids: {ranked_embedding_chunk_ids}, ranked_bm25_chunk_ids: {ranked_bm25_chunk_ids}")
    # take the first 60
    # rrf_score_dict_60 = {k: hit for k, hit in islice(rrf_score_dict.items(), 60)}
    to_fetch_1 = set()
    # for i in rrf_score_dict_60:
    for i in rrf_score_dict:
        if i in parent_indexes:
            to_fetch_1.add(i)
        if i in ranked_bm25_chunk_ids:
            if i not in vec_id_embed_dict:
                to_fetch_1.add(i)
    to_fetch_1 = list(to_fetch_1)
    fetched_dict = {}
    collection_name = get_collection_name(
        embedding_params.model_name,
        embedding_params.dimensions,
    )
    if to_fetch_1:
        vector_storage = VectorStorageFactory.get_instance()
        start_time = time.time()
        try:
            fetched = await vector_storage.index_fetch(
                ids=to_fetch_1,
                collection_name=collection_name,
                with_vector=False,
            )
            fetched_dict = {i.id: i for i in fetched}
        finally:
            logging.info(
                f"qdrant_index_fetch cost:{time.time() - start_time} s, collection_name: {collection_name}"
            )

    final_dict = {**vec_id_embed_dict, **fetched_dict}
    final_docs = []
    for hit_id in rrf_score_dict:
        embedding = final_dict.get(hit_id)
        # some bug causes data in es, but no in qdrant. adapt here
        if not embedding:
            logging.error(
                f"can not find embedding for id: {hit_id}, collection_name: {collection_name}"
            )
            continue
        # 存在子chunk的情况，子chunk不进入最终结果
        if embedding.metadata.get("parent_index") or embedding.metadata.get(
            "short_chunk"
        ):
            continue
        embedding.score = rrf_score_dict.get(hit_id)
        final_docs.append(embedding)

    # # this prefetch preserved especially for
    # case 1
    # very long continuous answer
    # dev bot: 20aa811c-1370-45c3-a19a-99ba137ef111
    # 详细列出使用打印机前的注意事项
    # 26 items, 1150 token answer
    #
    # case 2
    # price table mid parts can have low score in embeddings
    existing_chunks = OrderedSet(
        [(d.metadata.get("doc_id"), d.metadata.get("chunk_index")) for d in final_docs]
    )

    fetched_chunks, moved_docs = _resort_chunks(final_docs, existing_chunks)

    rearranged_final_docs = moved_docs + [
        doc for doc in final_docs if doc not in moved_docs
    ]
    # logging.info(
    #     f"single_dataset_result rearranged_final_docs: {rearranged_final_docs}, fetched_chunks: {fetched_chunks}, moved_docs: {moved_docs}"
    # )

    max_rrf_score = 100
    if rrf_score_dict:
        max_rrf_score = max(rrf_score_dict.values())
    try:
        fetched_chunks_after_rrf = []
        if final_docs:
            collection_name = final_docs[0].dataset_id
            to_fetch = list(fetched_chunks)
            fetched_chunks_after_rrf = await fetch_from_opensearch(
                collection_name, to_fetch
            )
            for chunk in fetched_chunks_after_rrf:
                chunk.score = 0.999999999
                chunk.metadata["rrf_max_score"]: int = max_rrf_score
                chunk.metadata["rrf_score"]: float = 0.0099999
                chunk.metadata["rrf_completed"]: bool = True
                chunk.metadata["embedding_retrieved"] = False
            logging.info(
                f"rrf phase fetch_from_opensearch {to_fetch=}, fetched: {len(fetched_chunks_after_rrf)}"
            )
    except Exception as e:
        fetched_chunks_after_rrf = []
        # 补充chunks序号完整性查询失败, 在opensearch出现异常时，使答案仍旧可以工作
        logging.error(f"dataset: fail to fetch chunks for order number in error:{e}")
    prefetched_final_docs = fetched_chunks_after_rrf + rearranged_final_docs
    # ! temp fix
    filtered_list = [x for x in prefetched_final_docs if x is not None]
    rrf_channel_count = 0
    if bm25_results:
        rrf_channel_count += 1
    if vector_hits:
        rrf_channel_count += 1
    max_embedding_score = 0
    if vec_id_embed_score_dict:
        max_embedding_score = max(vec_id_embed_score_dict.values())
    for chunk in filtered_list:
        if not chunk.metadata.get("rrf_completed"):
            chunk.metadata["rrf_channel_count"]: int = rrf_channel_count
            chunk.metadata["rrf_max_score"]: int = max_rrf_score
            chunk.metadata["rrf_score"]: float = rrf_score_dict.get(chunk.id)
            if chunk.id in vec_id_embed_score_dict:
                chunk.metadata["embedding_score"]: float = vec_id_embed_score_dict.get(
                    chunk.id
                )
                chunk.metadata["embedding_max_score"]: float = max_embedding_score
                chunk.metadata["embedding_retrieved"]: bool = True
            else:
                chunk.metadata["embedding_retrieved"]: bool = False
    return filtered_list


@trace()
def _resort_chunks(final_docs, existing_chunks):
    # for 3 highest scored embeddings, fetch next of them
    target_prefetch_cnt = 5
    fetched_chunks = set()
    moved_docs = []
    moved_docs_count = {}
    for i, res in enumerate(final_docs):
        # 0, 1, 2, 3, 4 valid
        if i > 5:
            break
        if len(fetched_chunks) >= target_prefetch_cnt:
            break
        doc_id = res.metadata.get("doc_id")
        next_chunk_index = res.metadata.get("chunk_index") + 1

        to_fetch = (doc_id, next_chunk_index)
        if to_fetch in existing_chunks:
            if doc_id in moved_docs_count and moved_docs_count[doc_id] >= 3:
                continue
            if len(moved_docs) >= 12:
                continue
            if to_fetch in fetched_chunks:
                continue
            # move doc to the front
            doc = None
            for item in final_docs:
                if (
                    item.metadata["doc_id"] == to_fetch[0]
                    and item.metadata["chunk_index"] == to_fetch[1]
                ):
                    doc = item
                    doc.score = res.score
                    break
            if not doc:
                logging.error("doc should not be none")
                sentry_sdk_capture_exception(ValueError("doc should not be none"))
            moved_docs.append(doc)
            # take next
            # next_to_fetch = (doc_id, next_chunk_index + 1)
            # if next_to_fetch in existing_chunks:
            #     # move doc to the front
            #     doc = None
            #     for item in final_docs:
            #         if (
            #             item.metadata["doc_id"] == next_to_fetch[0]
            #             and item.metadata["chunk_index"] == next_to_fetch[1]
            #         ):
            #             doc = item
            #             break
            #     if not doc:
            #         sentry_sdk_capture_exception(ValueError("doc should not be none"))
            #     moved_docs.append(doc)
        else:
            existing_chunks.add(to_fetch)
            fetched_chunks.add(to_fetch)
            if i == 0:
                existing_chunks.add((doc_id, next_chunk_index + 1))
                fetched_chunks.add((doc_id, next_chunk_index + 1))

    return fetched_chunks, moved_docs


def doc_id_text_merge(embeddings: List[Embeddings]) -> List[dict[str, str]]:
    # 将doc_id去重成一个列表（注意，需要保留第一次出现的顺序），然后将同一个doc_id的text进行拼接
    sort_doc_ids = []
    for embedding in embeddings:
        doc_id = embedding.metadata["doc_id"]
        if doc_id not in sort_doc_ids:
            sort_doc_ids.append(doc_id)

    docs = {}
    for embedding in embeddings:
        doc_id = embedding.metadata["doc_id"]
        if doc_id not in docs:
            docs[doc_id] = []
        docs[doc_id].append(embedding)

    # 根据chunk_index 进行排序
    for doc_id in docs:
        docs[doc_id] = sorted(docs[doc_id], key=lambda x: x.metadata["chunk_index"])

    # 拼接text
    res = []
    for doc_id in sort_doc_ids:
        if doc_id not in docs:
            continue
        # the first 30 chunks should be enough
        text = "".join(embedding.text for embedding in docs[doc_id][:30])
        title = docs[doc_id][0].metadata.get("title")
        if title is not None:
            text = title + "\n\n" + text
        # 替换连续空格或"-" >= 5 个为两个, 移除 parser_file URL，只保留括号内的文本
        # 因为 rerank window 只有 512 token，理论上这样做可能提高质量
        text = re.sub(r" {5,}", "  ", text)
        text = re.sub(r"-{5,}", "--", text)
        text = re.sub(r"!\[(.*?)]\(.*?parser_file.*?\)", r"\1", text)
        text = re.sub(r"!\(.*?parser_file.*?\)", "", text)
        item = {"text": text, "doc_id": doc_id}
        try:
            item["source"] = docs[doc_id][0].metadata["source"]
        except Exception as e:
            logging.error(f"doc_id: {doc_id} has no source")
        res.append(item)
    return res


custom_async_rerank_client = AsyncClientSession(
    api_key=settings.CUSTOM_RERANK_API_KEY,
    api_url=settings.CUSTOM_RERANK_ENDPOINT,
    timeout=600,
)


async def custom_rerank(
    query: str,
    documents: list,
    model_name: str = "hotchpotch/japanese-bge-reranker-v2-m3-v1",
):
    t1 = time.time()
    # The endpoint will error if len(documents) * max_chunks_per_doc >1,000, so max_chunks_per_doc is set to 8
    # response = await custom_async_rerank_client.rerank(query, documents, model_name, max_chunks_per_doc=8)
    response = await custom_async_rerank_client.rerank(query, documents, model_name)
    logging.info(
        f"custom rerank success response, time: {time.time() - t1}, remote internal time: {response.meta.get('time')}, doc_count: {len(documents)}"
    )
    return response


@trace()
async def do_rerank(
    enable_cohere_rerank: bool,
    dataset_results: List[Embeddings],
    query_key_words: str,
    search_options: List[SearchOption],
    ai_id: str,
    merged,
    seperated_dataset_results: list,
    cnt: int,
) -> dict:
    # v1/question/708cc14e-1d65-402f-9b83-bcaf69e728a1
    # 贵公司交通银行的账号是?
    # bad cases, most relevant doc rank low, following queries not work as expected at all
    # query_key_words = "bank account of the company's Bank of Communications"
    # query_key_words = "company, Bank of Communications, account number"
    # query_key_words = "The bank account of the company's Bank of Communications"

    def raw_response():
        doc_id_score_dict = {}
        for i, item in enumerate(dataset_results):
            doc_id = item.metadata["doc_id"]
            if (
                doc_id not in doc_id_score_dict
                or item.score > doc_id_score_dict[doc_id]["score"]
            ):
                doc_id_score_dict[doc_id] = {
                    "score": item.score,
                    "original_index": i,
                    "reranked_index": i,
                }
        return doc_id_score_dict

    if not enable_cohere_rerank:
        return raw_response()

    if not merged:
        logging.warning(f"merged len == 0: {merged}, return")
        return raw_response()
    if not COHERE_API_KEY and not settings.IS_USE_LOCAL_VLLM:
        logging.warning(f"no cohere api key and not local, no rerank")
        return raw_response()
    if settings.IS_USE_LOCAL_VLLM and not settings.CUSTOM_RERANK_ENDPOINT:
        logging.warning(f"no custom rerank endpoint, no rerank")
        return raw_response()
    if all(not item["text"] for item in merged):
        logging.warning(f"no text in merged text score dict, no rerank")
        return raw_response()
    merged_to_rerank = merged[:cnt]
    ratio_sum = sum(map(lambda x: len(x), seperated_dataset_results))
    ratio_list = [len(x) / ratio_sum for x in seperated_dataset_results]
    try:
        # Use custom rerank endpoint if both var is not empty
        if settings.IS_USE_LOCAL_VLLM:
            response = await custom_rerank(
                query_key_words,
                merged_to_rerank,
                settings.CUSTOM_RERANK_MODEL,
            )
        else:
            response = await parallel_rerank(
                dataset_results, merged_to_rerank, query_key_words
            )
        # Use reranked results
        doc_id_score_dict = {}
        min_score = 0
        for i, r in enumerate(response):
            doc_id_score_dict[r.document["doc_id"]] = {
                "score": r.relevance_score,
                "original_index": r.index,
                "reranked_index": i,
            }
            min_score = r.relevance_score
        s_i = len(doc_id_score_dict)
        for i, d in enumerate(dataset_results):
            if d.metadata["doc_id"] not in doc_id_score_dict:
                min_score = min_score * 0.9
                doc_id_score_dict[d.metadata["doc_id"]] = {
                    "score": min_score,
                    "original_index": i,
                    "reranked_index": s_i,
                }
                s_i += 1
        return doc_id_score_dict
    except Exception as e:
        logging.error(
            f"An error occurred during reranking: {e}; {traceback.format_exc()}"
        )
        sentry_sdk_capture_exception(e)
        # Fallback to using dataset_results without reranking, take max score of this doc as score
        return raw_response()


def get_sorted_boost_factor_dict(
    sorted_rrf_doc_id_score_dict, max_impact_const, key: str = "score_sum"
):
    max_rrf_score = next(iter(sorted_rrf_doc_id_score_dict.values()))[key]
    boost_factor_dict = {}
    for doc_id in sorted_rrf_doc_id_score_dict:
        score = sorted_rrf_doc_id_score_dict[doc_id][key]
        boost_factor = 1 + max_impact_const * score / max_rrf_score
        boost_factor_dict[doc_id] = boost_factor
    return boost_factor_dict


@trace()
async def dataset_chunks_query(
    ai_id: str,
    embedding_params: EmbeddingParams,
    search_options: list[SearchOption],
    openai_model: OpenAIModel,
    ai_obj: Optional[Robot] = None,
):
    current_time = time.time()
    # if search_options[0].query == search_options[0].query_key_words:
    #     search_options[0].query_key_words = None
    # 提取查询信息
    query_key_words = search_options[0].query_key_words
    query = search_options[0].query
    vector_collection_filter = search_options[0].vector_collection_filter
    questions = [search_options[0].query, query_key_words]

    # 获取OpenSearch客户端
    os_client = (
        get_open_search_client() if search_options[0].enable_opensearch else None
    )

    # 处理向量查询和BM25搜索
    if os_client and not vector_collection_filter:
        # 异步执行embedding_question与os_client.async_search
        results = await asyncio.gather(
            EmbeddingFactory.concurrent_embedding(questions, **embedding_params.dict()),
            dataset_bm25_search(os_client, search_options, True),
        )
        # 查询关键字向量嵌入 query key word vector embeddings
        query_key_words_embeddings = results[0]
        bm25_results_dict = results[1]  # opensearch最终融合结果 opensearch final fusion results
    else:
        bm25_results_dict = {}
        query_key_words_embeddings = await EmbeddingFactory.concurrent_embedding(
            questions, **embedding_params.dict()
        )

    # 执行向量查询
    vector_hits_dict = await vector_query_multi_dataset(
        search_options=search_options,
        embedding_params=embedding_params,
        query_embeddings=query_key_words_embeddings,
    )
    # logging.info(
    #     f'chunks_query get_indexs spend time:{time.time() - current_time}')
    logging.info(f"before search in search_options: {time.time() - current_time}")
    
    # 处理数据集结果
    dataset_results: list[Embeddings] = []
    seperated_dataset_results = []
    logging.info(
        f"dataset_chunks_query len(search_options): {len(search_options)}, search_options: {search_options}"
        # f"dataset_chunks_query len(search_options): {len(search_options)}, search_options: {search_options}, bm25_results_dict: {bm25_results_dict}"
    )


    # 获取上下文长度
    context_limit = get_llm_context_length(openai_model)

    top_vec_cnt = int(context_limit / 300)
    top_vector_queue = []

    # 处理每个搜索选项
    for idx, option in enumerate(search_options):
        bm25_results = bm25_results_dict.get(option.collection_name, [])
        vector_hits = vector_hits_dict.get(option.collection_name, [])
        
        # 保留高分向量
        for hit in vector_hits:
            if len(top_vector_queue) < top_vec_cnt:
                heapq.heappush(top_vector_queue, (hit.score, hit.id))
            elif hit.score > top_vector_queue[0][0]:
                heapq.heappushpop(top_vector_queue, (hit.score, hit.id))
        
        # 处理单个数据集结果
        sdr = await single_dataset_result(
            copy.deepcopy(vector_hits), copy.deepcopy(bm25_results), embedding_params
        )
        dataset_results.extend(sdr)
        # logging.info(
        # f"bm25_results: {bm25_results}, vector_hits: {vector_hits}, sdr: {sdr}"
        # f"bm25_results: {bm25_results}, vector_hits: {vector_hits}"
        # )
        seperated_dataset_results.append(sdr)

    # If some chunks have high embedding scores but low opensearch/rrf scores
    # we should still consider keeping them for later processing phases
    # e.g. case aliyun gwc: https://u0vocx8xrmg.feishu.cn/record/RryVrY1QSecpoacDpQccuwrwnNe
    # 获取分数最高的嵌入ID
    top_score_vector_ids = set(vid for _, vid in top_vector_queue)

    # 按分数排序数据集结果
    dataset_results = sorted(dataset_results, key=lambda r: -r.score)
    #     for hit in vector_hits: 

    ## 检查所有嵌入是否都有doc_id
    all_embeddings_have_doc_id = all(
        embedding.metadata and "doc_id" in embedding.metadata
        for embedding in dataset_results
    )
    if not all_embeddings_have_doc_id:
        # some old data, no fill gap or further sort
        logging.warning(
            f"not all_embeddings_have_doc_id, no further process for old data"
        )
        return dataset_results

    total_length_vector_hits_dict = sum(
        len(value) if value else 0 for value in vector_hits_dict.values()
    )
    total_length_bm25_results_dict = sum(
        len(value) if value else 0 for value in bm25_results_dict.values()
    )
    # logging.info(
    #     f"dataset_results: total_length_vector_hits_dict: {total_length_vector_hits_dict}, vector_hits_dict: {vector_hits_dict}, total_length_bm25_results_dict: {total_length_bm25_results_dict}, bm25_results_dict: {bm25_results_dict}"
    # )
    logging.info(
        f"dataset_results: total_length_vector_hits_dict: {total_length_vector_hits_dict}, total_length_bm25_results_dict: {total_length_bm25_results_dict}"
    )

    # 如果没有结果，返回空列表
    if len(dataset_results) == 0:
        logging.warning(f"dataset_results len == 0: {dataset_results}, return")
        return dataset_results

    # 应用文档限制
    # TODO move this after reranking
    # for every doc, retain first N large chunks, avoid too much chunk for very long(hundreds of pages) docs
    limited_dataset_results, distinct_doc_count = _doc_processor_apply_document_limits(openai_model, dataset_results, top_score_vector_ids)
    # according to rrf alg, filter out single search result chunks ranked > 100, low enough to not exclude useful chunks
    # score_factor = await get_score_factor(bm25_results_dict, vector_hits_dict)
    
    score_limit = 0
    # score_limit = 0.001 * score_factor
    # 过滤低分结果
    score_limited_dataset_results = [
        i for i in limited_dataset_results if i.score >= score_limit
    ]
    # scores = [v.score for v in dataset_results]
    # limited_scores = [v.score for v in score_limited_dataset_results]
    # logging.info(
    #     f"score_factor: {score_factor}, score_limit: {score_limit}, original_len: {len(dataset_results)}, limited_len: {len(score_limited_dataset_results)}, original_scores: {scores} \n {limited_scores=}"
    # )

    # dataset_result_to_rerank = [d for d.score > 0.02 dataset_results]
    # dataset_result_to_rerank = []
    # doc_id_token_count = {}
    # for d in dataset_results:
    #     doc_id = d.metadata["doc_id"]
    #     if doc_id in doc_id_ds_count:
    #         if doc_id_ds_count[doc_id]:
    #             continue
    #         doc_id_ds_count[doc_id] += 1
    #     if d.score > 0.02:
    #         dataset_result_to_rerank.append(d)
    #     count_token(openai_model, d.text)
    # dataset_result_to_rerank = [d for d in dataset_results if d.score > 0.02]

    # after search, before rerank, add chunks from the same section
    if settings.DINAMIC_SECTION_RETRIEVE:
        score_limited_dataset_results = await _section_processor_process_sections(embedding_params, score_limited_dataset_results)

    # 确定是否启用重排名
    enable_cohere_rerank = _rerank_operations_should_enable_rerank(search_options, ai_obj, distinct_doc_count)

    # 合并文档
    merged = doc_id_text_merge(score_limited_dataset_results)
    logging.info(f"docs merged len: {len(merged)}")

    # 执行重排名
    dr1 = do_rerank(
        enable_cohere_rerank,
        dataset_results,
        query_key_words,
        search_options,
        ai_id,
        merged,
        seperated_dataset_results,
        150,
    )
    dr2 = do_rerank(
        enable_cohere_rerank,
        dataset_results,
        query,
        search_options,
        ai_id,
        merged,
        seperated_dataset_results,
        56,
    )

    # 获取重排名结果
    res = await asyncio.gather(dr1, dr2)
    doc_id_score_dict_0 = res[0]
    doc_id_score_dict_1 = res[1]
    doc_id_score_dict = doc_id_score_dict_0.copy()
    for doc_id in doc_id_score_dict_1:
        doc_id_score_dict[doc_id]["score"] = max(
            doc_id_score_dict[doc_id]["score"], doc_id_score_dict_1[doc_id]["score"]
        )
    
    # 按分数排序
    sorted_doc_id_score_dict = dict(
        sorted(
            doc_id_score_dict.items(), key=lambda item: item[1]["score"], reverse=True
        )
    )

    # 提升RRF分数
    sorted_new_doc_id_score_dict = _rerank_operations_boost_rrf_score(ai_id, query, dataset_results, doc_id_score_dict, sorted_doc_id_score_dict)

    # 复制数据集结果
    copied_limited_dataset_results = copy.deepcopy(score_limited_dataset_results)

    # 更新数据集结果分数
    for item in copied_limited_dataset_results:
        doc_id = item.metadata["doc_id"]
        item.score = sorted_new_doc_id_score_dict.get(doc_id, {"score": 0})["score"]
    # 按分数排序
    sorted_dataset_results = sorted(
        copied_limited_dataset_results,
        key=lambda r: (-r.score, r.metadata["doc_id"], r.metadata["chunk_index"]),
    )

    # filter but retain at least first doc
    # 9c3da88a-3378-4f12-ac91-2e0781cf7feb 文档内容是什么
    max_score = sorted_dataset_results[0].score
    first_doc_id = sorted_dataset_results[0].metadata["doc_id"]

    if enable_cohere_rerank:
        # only filter by score if cohere reranked
        pre_filtered_dataset_results = [
            i
            for i in sorted_dataset_results
            if i.score > 1 / 8 * max_score
            and (i.metadata["doc_id"] == first_doc_id or i.score > 0.001)
        ]
    else:
        pre_filtered_dataset_results = sorted_dataset_results.copy()
    filtered_dataset_results = pre_filtered_dataset_results

    # sorted_results = await _sort_chunks_in_write_order_and_deduplicate_chunks(
    #     filtered_dataset_results
    # )
    # gap_filled_results = await _fill_gaps(
    #     search_options, sorted_results, dataset_results, vector_length=1536
    # )
    # for item in gap_filled_results:
    #     doc_id = item.metadata["doc_id"]
    #     item.score = sorted_new_doc_id_score_dict.get(doc_id, {"score": 0})["score"]
    # 向顶级文档添加额外的块
    sorted_filtered_dataset_results = _doc_processor_add_embedding_chunks_to_top_doc(dataset_results, doc_id_score_dict, sorted_new_doc_id_score_dict, filtered_dataset_results)

    logging.info(f"dataset_chunks_query total spend time:{time.time() - current_time}")
    return sorted_filtered_dataset_results

def _doc_processor_add_embedding_chunks_to_top_doc(dataset_results, doc_id_score_dict, sorted_new_doc_id_score_dict, filtered_dataset_results):
    """
    向顶级文档添加嵌入区块
    
    Args:
        dataset_results: 原始数据集嵌入结果列表
        doc_id_score_dict: 文档ID到分数数据的映射字典
        sorted_new_doc_id_score_dict: 按分数排序的文档ID分数字典
        filtered_dataset_results: 过滤后的结果列表
        
    Returns:
        添加了额外区块的结果列表
    """
    sorted_filtered_dataset_results = filtered_dataset_results
    if len(doc_id_score_dict) >= 2:
        # get first score and second score
        sorted_scores = [
            (doc_id, data["score"])
            for doc_id, data in sorted_new_doc_id_score_dict.items()
        ]
        doc0_id, doc0_rerank_score = sorted_scores[0]
        _, doc1_rerank_score = sorted_scores[1]
        if doc0_rerank_score > 1.5 * doc1_rerank_score and doc0_rerank_score >= 0.7:
            doc0_embedding_retrieved_chunks = sorted(
                [
                    c
                    for c in dataset_results
                    if c.metadata["doc_id"] == doc0_id
                    and c.metadata["embedding_retrieved"]
                ],
                key=lambda i: i.metadata["embedding_score"],
                reverse=True,
            )
            doc0_embedding_retrieved_chunk_indexes = [
                c.metadata["chunk_index"] for c in doc0_embedding_retrieved_chunks
            ]
            accepted_doc0_chunk_indexes = [
                c.metadata["chunk_index"]
                for c in filtered_dataset_results
                if c.metadata["doc_id"] == doc0_id
            ]
            accepted_doc0_chunk_cnt = len(accepted_doc0_chunk_indexes)
            for c in doc0_embedding_retrieved_chunks:
                if accepted_doc0_chunk_cnt >= 25:
                    break
                else:
                    if (
                        c.metadata["chunk_index"] not in accepted_doc0_chunk_indexes
                        and (
                            c.metadata["embedding_score"]
                            / c.metadata["embedding_max_score"]
                        )
                        >= 0.7
                    ):
                        accepted_doc0_chunk_cnt += 1
                        accepted_doc0_chunk_indexes.append(c.metadata["chunk_index"])
                        c.score = doc0_rerank_score
                        filtered_dataset_results.append(c)
            logging.info(
                f"filtered_doc0_chunk_cnt: {accepted_doc0_chunk_cnt}, {len(accepted_doc0_chunk_indexes)}, {accepted_doc0_chunk_indexes}"
            )
            sorted_filtered_dataset_results = sorted(
                filtered_dataset_results,
                key=lambda r: (
                    -r.score,
                    r.metadata["doc_id"],
                    r.metadata["chunk_index"],
                ),
            )
            
    return sorted_filtered_dataset_results

def _rerank_operations_boost_rrf_score(ai_id, query, dataset_results, doc_id_score_dict, sorted_doc_id_score_dict):
    """
    提升RRF分数
    
    Args:
        ai_id: AI ID
        query: 查询
        dataset_results: 数据集结果
        doc_id_score_dict: 文档ID分数字典
        sorted_doc_id_score_dict: 按分数排序的文档ID分数字典
        
    Returns:
        提升后的文档ID分数字典
    """
    new_doc_id_score_dict = copy.deepcopy(sorted_doc_id_score_dict)
    # boost with rrf score
    if len(sorted_doc_id_score_dict) >= 2:
        scores = [v["score"] for v in sorted_doc_id_score_dict.values()]
        log_score = f"ai: {ai_id}, query: {query}, {scores[:5]}"
        if scores[0] > 0.999999:
            # dev bot d70c3cff-68dd-4d9a-ade6-0c94b7a014e5
            # DozoFreesh Fruits Tea Set Of 6Bags
            # https://www.freesh.com/product-76.html, scores[0] == 0.9999998
            logging.info(f"very high score: no boost")
        elif scores[0] > 0.995:
            boost_factor_1 = 1
            if scores[0] > 0.9995:
                logging.info(f"score: {log_score}, very related, smaller boost")
                boost_factor_1 = 0.5
            if scores[0] - scores[1] > 0.01:
                logging.info(f"score: {log_score}, diff enough")
            elif (
                scores[0] >= 0.995
                and scores[1] >= 0.994
                and scores[0] - scores[1] < 0.002
            ):
                logging.info(f"score: {log_score}, has tie, boost")
                dataset_results_scored = list(
                    filter(lambda x: x.score != 0, dataset_results)
                )
                sorted_dataset_results_scored = list(
                    sorted(dataset_results_scored, key=lambda a: a.score, reverse=True)
                )
                rrf_doc_id_score_dict = {}
                doc_id_count = {}
                doc_id_count_limit = 5
                for index, item in enumerate(sorted_dataset_results_scored):
                    if item.score == 0:
                        continue
                    doc_id = item.metadata["doc_id"]
                    if doc_id in doc_id_count:
                        if doc_id_count[doc_id] >= doc_id_count_limit:
                            continue
                        else:
                            doc_id_count[doc_id] += 1
                    else:
                        doc_id_count[doc_id] = 1
                    v = rrf_doc_id_score_dict.get(doc_id)
                    if not v:
                        v = {
                            "score_sum": 0,
                            "count": 0,
                            "score_list": [],
                            "score_max": 0,
                            "score_sum_e": 0,
                            "score_mul_e": 1,
                            "score_mul_e_list": [],
                            "score_mul_e_rank_list": [],
                        }
                    # shouldn't add all scores, deal with long text but unrelated doc, limit max 5-chunk score sum
                    v["score_sum"] += math.pow(item.score * 60, math.e)
                    if len(v["score_mul_e_list"]) < 2:
                        s = math.pow((1 / (index + 60)) * 60, math.e)
                        v["score_mul_e"] += s
                        v["score_mul_e_list"].append(s)
                        v["score_mul_e_rank_list"].append(index)
                    v["score_list"].append(item.score)
                    v["count"] += 1
                    v["score_sum_e"] += math.exp(1 + item.score)
                    v["score_max"] = max(v["score_max"], item.score)
                    v["score_sum_e_squared_avg"] = v["score_sum_e"] / v["count"]
                    rrf_doc_id_score_dict[doc_id] = v
                # First, sort the dictionary by score_sum in descending order
                rrf_doc_id_score_dict_sorted_desc_by_score_sum = dict(
                    sorted(
                        rrf_doc_id_score_dict.items(),
                        key=lambda item: item[1]["score_max"],
                        reverse=True,
                    )
                )
                for rank, (doc_id, v) in enumerate(
                    rrf_doc_id_score_dict_sorted_desc_by_score_sum.items()
                ):
                    v["score_list"] = sorted(v["score_list"], reverse=True)
                    v["rank"] = rank
                    v["score"] = 1 / (60 + rank)

                max_impact_factor = 0.038 * boost_factor_1
                sorted_boost_factor_dict = get_sorted_boost_factor_dict(
                    rrf_doc_id_score_dict_sorted_desc_by_score_sum,
                    max_impact_factor,
                    "score",
                )

                boosted_doc_id = set()
                for doc_id in doc_id_score_dict:
                    score = doc_id_score_dict[doc_id]["score"]
                    if score < 0.96:
                        break
                    boosted_doc_id.add(doc_id)
                    new_doc_id_score_dict[doc_id]["score"] *= sorted_boost_factor_dict[
                        doc_id
                    ]
                for rank, doc_id in enumerate(
                    rrf_doc_id_score_dict_sorted_desc_by_score_sum
                ):
                    if rank >= 10:
                        break
                    if doc_id in boosted_doc_id:
                        continue
                    new_doc_id_score_dict[doc_id]["score"] *= sorted_boost_factor_dict[
                        doc_id
                    ]
        elif scores[0] < 0.01:
            # case: 贵公司交通银行的账号是， max=0.007
            doc_id_set = set()
            logging.info(
                f"first_value['score'] < threshold, adjust score with rrf result"
            )
            for d in dataset_results[:20]:
                doc_id = d.metadata["doc_id"]
                if not doc_id in doc_id_set:
                    doc_id_set.add(doc_id)
                    new_doc_id_score_dict[doc_id]["score"] += d.score * 10
    # 按分数排序
    sorted_new_doc_id_score_dict = dict(
        sorted(
            new_doc_id_score_dict.items(),
            key=lambda item: item[1]["score"],
            reverse=True,
        )
    )
    return sorted_new_doc_id_score_dict

def _rerank_operations_should_enable_rerank(search_options, ai_obj, distinct_doc_count):
    """
    判断是否应该启用重排序
    
    Args:
        search_options: 搜索选项列表
        ai_obj: AI对象
        distinct_doc_count: 文档数量
        
    Returns:
        是否应该启用重排序
    """
    enable_cohere_rerank = search_options[0].enable_rerank
    if distinct_doc_count <= 1:
        if (
            ai_obj
            and ai_obj.get_config(AIConfigType.ENABLE_WEB_SEARCH).lower() != "true"
        ):
            logging.info(
                f"total {distinct_doc_count} doc(s) and no web search, no rerank"
            )
            enable_cohere_rerank = False
    return enable_cohere_rerank

async def _section_processor_process_sections(embedding_params, score_limited_dataset_results):
    """
    处理数据集结果中的区块
    
    Args:
        score_limited_dataset_results: 按分数限制的数据集结果
        embedding_params: 嵌入参数
        
    Returns:
        处理区块后的数据集结果
    """
    logging.info(
            f"sections process start, chunks count before: {len(score_limited_dataset_results)}"
        )
    section_time = time.time()
    section_dataset_results = copy.deepcopy(score_limited_dataset_results)
    section_dataset_results_ids = [item.id for item in section_dataset_results]
    section_chunks_to_fill = []

    datasets_to_fill = []
    files_to_fill = []
    sections_to_fill = []

    for item in section_dataset_results:
        item_secs = item.metadata.get("sections", [])
        if len(item_secs) > 0:
            for sec in item_secs:
                datasets_to_fill.append(item.dataset_id)
                files_to_fill.append(item.metadata["file_id"])
                sections_to_fill.append(
                        {
                            "sid": item.metadata["file_id"] + "_" + str(sec["index"]),
                            "section": sec,
                            "score": item.score,
                        }
                    )
    if len(datasets_to_fill) > 0:
        datasets_to_fill = list(set(datasets_to_fill))
        files_to_fill = list(set(files_to_fill))
            # 使用sid进行去重
        sections_to_fill = list({v["sid"]: v for v in sections_to_fill}.values())
            # 获取chunk所在file, 找到chunk所在section的其他chunk
        collection_name = get_collection_name(
                embedding_params.model_name,
                embedding_params.dimensions,
            )
        chunks = await get_chunks_by_group_and_file(
                collection_name, datasets_to_fill, files_to_fill
            )
        for chunk in chunks:
            if chunk.metadata["file_id"] and chunk.metadata.get("sections"):
                chunk_sids = [
                        chunk.metadata["file_id"] + "_" + str(sec["index"])
                        for sec in chunk.metadata["sections"]
                    ]
                for section in sections_to_fill:
                        # if section["sid"] in chunk_sids and chunk.id not in section_dataset_results_ids:
                    if section["sid"] in chunk_sids:
                        chunk.score = section["score"]
                        section_chunks_to_fill.append(chunk)
        # 补充chunks
    if len(section_chunks_to_fill) > 0:
        chunks_to_fill = []
        unique_chunks = {chunk.id: chunk for chunk in section_chunks_to_fill}
        section_chunks_to_fill = list(unique_chunks.values())
        del unique_chunks
        for chunk in section_chunks_to_fill:
            if chunk.id not in section_dataset_results_ids:
                chunks_to_fill.append(chunk)
        score_limited_dataset_results.extend(chunks_to_fill)
    del (
            section_dataset_results,
            section_chunks_to_fill,
            datasets_to_fill,
            files_to_fill,
            sections_to_fill,
            section_dataset_results_ids,
        )
    logging.info(
            f"sections process end, chunks count after: {len(score_limited_dataset_results)}, time: {time.time() - section_time}"
        )
    return score_limited_dataset_results

def _doc_processor_apply_document_limits(openai_model, dataset_results, top_score_vector_ids):
    """
    应用文档限制，确保不超过token限制

    Args:
        dataset_results: 数据集嵌入结果列表
        openai_model: OpenAI模型名称
        
    Returns:
        应用限制后的文档列表
    """
    limited_dataset_results = []
    distinct_doc_count = len(set([res.metadata["doc_id"] for res in dataset_results]))
    max_final_chat_tokens = get_llm_context_length(openai_model)

    token_limit = max_final_chat_tokens
    # max_token_per_doc = 2000
    # if token_limit > 6000:
    #     max_token_per_doc = 3000
    # if token_limit > 8000:
    #     max_token_per_doc = 4000
    # if token_limit > 10000:
    #     max_token_per_doc = 5000
    # if token_limit > 12000:
    #     max_token_per_doc = 6000
    # if token_limit > 14000:
    #     max_token_per_doc = 7000
    # if token_limit > 16000:
    #     max_token_per_doc = 8000
    # single doc optimization
    if distinct_doc_count == 1:
        # use all chunks
        token_count = 0
        for res in dataset_results:
            token_count += num_tokens_from_string(res.text)
            if token_count <= token_limit:
                limited_dataset_results.append(res)
    else:
        # todo: limit by token count rather than chunk count
        single_doc_chunk_soft_limit = 10
        single_doc_chunk_hard_limit = 20
        # if distinct_doc_count == 2:
        #     single_doc_chunk_soft_limit = 12
        # elif openai_model == "gpt_4_turbo" or openai_model == "gpt_4o":
        #     single_doc_chunk_soft_limit = 12
        # elif openai_model == "gpt_4":
        #     single_doc_chunk_soft_limit = 8
        cnt_map = {}
        for res in dataset_results:
            doc_id = res.metadata["doc_id"]
            cnt = (cnt_map.get(doc_id) or 0) + 1
            cnt_map[doc_id] = cnt
            if (
                cnt <= single_doc_chunk_soft_limit
                or (
                    (not res.metadata.get("rrf_completed"))
                    and (
                        res.metadata["rrf_score"] / res.metadata["rrf_max_score"] >= 0.7
                    )
                    and cnt < single_doc_chunk_hard_limit
                )
                or (
                    res.id in top_score_vector_ids and cnt < single_doc_chunk_hard_limit
                )
            ):
                limited_dataset_results.append(res)
    return limited_dataset_results,distinct_doc_count


async def get_score_factor(bm25_results_dict, vector_hits_dict):
    total_length_vector_hits_dict = sum(
        len(value) if value else 0 for value in vector_hits_dict.values()
    )
    total_length_bm25_results_dict = sum(
        len(value) if value else 0 for value in bm25_results_dict.values()
    )
    logging.info(
        f"total_length_vector_hits_dict: {total_length_vector_hits_dict}, total_length_bm25_results_dict: {total_length_bm25_results_dict}"
    )

    score_factor = 0
    if total_length_bm25_results_dict:
        score_factor += 1
    if total_length_vector_hits_dict:
        score_factor += 1
    return score_factor


# co = cohere_utils.Client(COHERE_API_KEY)
co = AsyncClientSession(COHERE_API_KEY) if COHERE_API_KEY else None


async def single_request(
    query_key_words: str, documents: List[Dict[str, str]], model_name: str
):
    start_time = time.time()
    # The endpoint will error if len(documents) * max_chunks_per_doc >10,000 where max_chunks_per_doc is set to 10 as default
    response = await co.rerank(
        query_key_words, documents, model_name, max_chunks_per_doc=65
    )
    elapsed_time = time.time() - start_time
    return response, elapsed_time


async def parallel_rerank(dataset_results, merged, query_key_words):
    s_time = time.time()
    docs_per_request = 15
    # model_name = "rerank-multilingual-v3.0"
    model_name = "rerank-v3.5"

    tasks = []
    n_chunks = len(merged) // docs_per_request + (
        1 if len(merged) % docs_per_request > 0 else 0
    )

    for i in range(0, n_chunks):
        chunk = merged[i * docs_per_request : (i + 1) * docs_per_request]
        tasks.append(
            asyncio.ensure_future(single_request(query_key_words, chunk, model_name))
        )

    responses = await asyncio.gather(*tasks)

    aggregated_response = []
    timings = [resp[1] for resp in responses]
    max_time = max(timings)
    n = len(responses)

    for response, _ in responses:
        aggregated_response.extend(response)

    logging.info(
        f"cohere sdk: rerank [{query_key_words}] with {len(dataset_results)} large chunks, {len(merged)} partial docs spend time:{time.time() - s_time}"
    )
    start_time1 = time.time()
    str_lengths = [
        sum(len(result.document["text"]) for result in resp[0].results)
        for resp in responses
    ]
    search_units = [
        response[0].meta["billed_units"]["search_units"] for response in responses
    ]
    # tiktoken_lengths = [
    #     sum(
    #         num_tokens_from_string(result.document["text"])
    #         for result in resp[0].results
    #     )
    #     for resp in responses
    # ]
    logging.info(
        f"cohere sdk: parallel rerank, model: {model_name}, full duration {time.time() - s_time}, totally sent {n} requests, timing-str_lengths: {[*zip(str_lengths, timings, search_units)]}, max request time {max_time}, token_count total time: {time.time() - start_time1}"
    )
    sorted_aggregated_response = sorted(
        aggregated_response, key=lambda x: x.relevance_score, reverse=True
    )
    return sorted_aggregated_response


async def chunks_query(options: ContextQueryOptions, ai_obj: Robot = None, dataset_objs: List[Dataset] = None):
    if not dataset_objs:
        dataset_objs = await Dataset.filter(robots__id=options.ai_id)
    search_options = []
    for dataset_obj in dataset_objs:
        vector_storage_type = VectorStorageType(
            dataset_obj.metadata.get("vector_storage", VectorStorageType.PINECONE)
        )
        if dataset_obj.data_status == DATASET_STATUS.FROZEN:
            continue
        embeddings_model = dataset_obj.metadata.get(
            "embeddings_model", EMBEDDINGS_MODEL.OPENAI
        )
        source_lang = dataset_obj.metadata.get("source_lang")

        analyzer = get_analyzer(source_lang)
        search_options.append(
            SearchOption(
                dataset_id=str(dataset_obj.id),
                collection_name=dataset_obj.collection_name,
                query=options.query,
                query_key_words=options.query_key_words,
                analyzer=analyzer,
                vector_storage_type=vector_storage_type,
                embeddings_model=embeddings_model,
                count=options.count,
                score=options.score,
                query_faq_answer_with_embeddings=options.query_faq_answer_with_embeddings,
                vector_collection_filter=options.vector_collection_filter,
                enable_rerank=options.enable_rerank,
                enable_opensearch=options.enable_opensearch,
            )
        )
    if not search_options:
        logging.info("no search_options, return empty context")
        return []
    embedding_params = EmbeddingParams(
        provider=options.embeddings_model,
        model_name=options.embedding_model_name,
        dimensions=options.embedding_dimensions,
    )
    return await dataset_chunks_query(
        options.ai_id, embedding_params, search_options, options.openai_model, ai_obj
    )


async def get_search_options(
    ai_id: str, score: float, query: str, query_key_words: str
):
    dataset_objs = await Dataset.filter(robots__id=ai_id)
    search_options = []
    for dataset_obj in dataset_objs:
        vector_storage_type = VectorStorageType(
            dataset_obj.metadata.get("vector_storage", VectorStorageType.PINECONE)
        )
        if dataset_obj.data_status == DATASET_STATUS.FROZEN:
            continue
        embeddings_model = dataset_obj.metadata.get(
            "embeddings_model", EMBEDDINGS_MODEL.OPENAI
        )
        source_lang = dataset_obj.metadata.get("source_lang")

        analyzer = get_analyzer(source_lang)
        search_options.append(
            SearchOption(
                dataset_id=str(dataset_obj.id),
                collection_name=dataset_obj.collection_name,
                query=query,
                query_key_words=query_key_words,
                analyzer=analyzer,
                vector_storage_type=vector_storage_type,
                # embeddings_model=embeddings_model,
                count=8,
                score=score,
                # enable_opensearch=enable_opensearch,
            )
        )
    if not search_options:
        logging.info("no search_options, return empty search_options")
        return []
    return search_options


async def get_chunks_by_group_and_file(
    collection_name: str,
    group_id_list: List[str],
    file_id_list: List[str],
    batch_size: int = 100,
) -> List[Embeddings]:
    """获取指定 group_id_list 和 file_id_list 的所有数据
    Args:
        collection_name: 集合名称
        group_id_list: 分组ID列表
        file_id_list: 文件ID列表
        batch_size: 每次获取的数量
    Returns:
        List[Embeddings]: 所有匹配的数据点
    """
    try:
        vs = VectorStorageFactory.get_instance()

        filters = {
            "must": [
                {"key": "group_id", "match": {"any": group_id_list}},
                {"key": "metadata.file_id", "match": {"any": file_id_list}},
            ]
        }

        all_points = []
        offset = None

        while True:
            try:
                response = await vs.index_points(
                    collection_name=collection_name,
                    offset=offset,
                    filters=filters,
                    limit=batch_size,
                    with_payload=True,
                    with_vectors=True,
                )

                if not response.result:
                    break

                points = [process_point(point) for point in response.result]
                all_points.extend(points)

                if len(response.result) < batch_size:
                    break

                offset = response.result[-1].id

            except Exception as e:
                logging.error(f"Error fetching batch with offset {offset}: {str(e)}")
                sentry_sdk_capture_exception(e)
                break

        return all_points

    except Exception as e:
        logging.error(
            f"Error in get_chunks_by_group_and_file: collection_name={collection_name}, "
            f"group_id_list={group_id_list}, file_id_list={file_id_list}, error={str(e)}"
        )
        sentry_sdk_capture_exception(e)
        return []


# async def main():
#     async with AsyncClientSession(COHERE_API_KEY) as co:
#         result = await co.parallel_rerank(
#             "a",
#             [{"text": "a", "doc_id": "872"}, {"text": "b", "doc_id": 123}],
#             "rerank-multilingual-v2.0",
#         )
#         print(result)


# if __name__ == "__main__":
#     asyncio.run(main())
