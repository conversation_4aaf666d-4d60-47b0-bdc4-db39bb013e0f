import asyncio
import copy
import datetime
import os
import re
import time
import traceback
import uuid
from typing import List, Optional, AsyncIterator, OrderedDict
from uuid import UUID

import aiohttp
import boto3
import openai
import requests
from botocore.exceptions import ClientError
from email_validator import validate_email, EmailNotValidError
from fastapi import (
    APIRouter,
    Body,
    Depends,
    File,
    Form,
    Header,
    HTTPException,
    Request,
    Security,
    UploadFile,
)
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi_auth0 import Auth0User
from googleapiclient.discovery import build
from langchain.schema import HumanMessage, SystemMessage
from langchain_core.messages import AIMessage
from langchain_core.messages import convert_to_messages
from loguru import logger as logging
from pydantic import BaseModel
from skywalking.decorators import trace
from starlette.status import HTTP_204_NO_CONTENT
from tortoise.contrib.pydantic import pydantic_model_creator
from tortoise.exceptions import DoesNotExist
from tortoise.query_utils import Prefetch
from tortoise.queryset import Q
from tortoise.transactions import in_transaction

from mygpt import settings
from mygpt.agent_exp.utils.using_util import prepare_answer2chatchunk
from mygpt.agent_functioncall.callback_handler import (
    DigitalHumanFCAgentStreamCallbackHandler,
    FCAgentBaseCallbackHandler,
)
from mygpt.agent_functioncall.functioncall_agent_engine import AgentFunctionCallEngine
from mygpt.agent_functioncall.schemas.prompt_schemas import PromptType
from mygpt.authorization import (
    depend_api_key_with_none,
    get_current_oauth_user_with_gbase_user,
    verify_admin_access,
)
from mygpt.endpoints.faqs import references_faq
from mygpt.enums import (
    AIConfigType,
    EMBEDDINGS_MODEL,
    FAQ_TYPE,
    MESSAGE_COMES_FROM,
    ExcelTestTaskStatus,
    MessagePageInfo,
    OpenAIModel,
    SessionMessageStatus,
    UserIntent,
    RobotType,
    ResourceType,
    StreamingMessageDataFormat,
    FunctionCallStyle,
)
from mygpt.error import (
    InvalidParameterException,
    NotFoundException,
    OperationFailedException,
    UnsupportedException,
    sentry_sdk_capture_exception,
)
from mygpt.file_doc_search import chunks_query, AsyncClientSession
from mygpt.loader.service import split_by_file
from mygpt.models import (
    Apikey,
    Dataset,
    ExcelTestTask,
    Faqs,
    InsiderPreviewUser,
    QuestionRecommended,
    Robot,
    Session,
    SessionMessage,
    SessionMessageMetadata,
    SessionUser,
    TimedExcelTestFile,
    VectorFile,
    User,
    AccountMember,
)
from mygpt.openai_utils import (
    chat_acreate,
    get_chat_history,
    get_chat_history_turbo,
    get_final_chat_context,
    user_intent,
    user_intent_detect,
    acquire_message_lock,
    release_message_lock,
    question_answer_with_function_call,
)
from mygpt.opensearch_faq import OpenSearchFaqClient
from mygpt.parameters import ListAPIParams, Page, tortoise_paginate
from mygpt.prompt import (
    ANSWER_ASK_ABOUT_BOT_SYSTEM_PROMPT,
    fallback_with_general_knowledge_final_question_prompts,
    fallback_with_general_knowledge_final_question_prompts_with_image,
    get_extra_goal,
    get_question_dictionary_prompt,
    get_question_environments,
    get_talking_style,
    get_unknown_text,
    gpt_4_turbo_fallback_with_general_knowledge_final_question_prompts,
    gpt_4_turbo_fallback_with_general_knowledge_with_image_final_question_prompts,
    question_recommended_sys_template,
    question_recommended_user_template,
    session_message_title_sys_template,
    session_message_title_user_template,
    turbo_function_call_get_reference_index_prompt,
    turbo_function_call_get_reference_user,
    turbo_system_content,
    turbo_user_prompt,
    gpt_4_turbo_final_question_prompts_with_image,
    digital_human_robot_default_prompt,
    gpt_4_turbo_final_question_prompts,
    turbo_function_call_get_reference_document_with_title,
    turbo_function_call_get_reference_document,
)
from mygpt.schemata import (
    ApiKey,
    ContextQueryOptions,
    FinalQuestionParams,
    QuestionIn,
    QuestionOut,
    RecommendedOut,
    RecommendIn,
    SearchResult,
    SessionMessageExpandOut,
    SessionMessageExpandWithSessionOut,
    SessionOut,
    SessionOutExpand,
    VectorCollectionFilter,
    Embeddings,
    QueryEmbeddingsIn,
    SessionMessageExpandWithSessionHistoryOut,
    QuestionAgent,
)
from mygpt.send_email import send_human_agent_notification_email
from mygpt.service.chat.faq_service import recommend_translation
from mygpt.service.chat.fast_faq_service import fast_faq
from mygpt.service.quality_assessment import save_to_quality_assessment_by_message_id
from mygpt.service.rag_control import answer_rag_bot
from mygpt.session_metrics import (
    create_or_update_session_message_count,
    create_or_update_session_rating_count,
    create_or_update_session_score,
    create_or_update_session_user_count,
    create_session_message_page_access_metrics,
)
from mygpt.settings import (
    GOOGLE_CUSTOM_SEARCH_API_KEY,
    COHERE_API_KEY,
    AZURE_STT_TOKEN_SUBSCRIPTION_KEY,
    QUOTA_CHECK,
)
from mygpt.site_crawler import SiteCrawler
from mygpt.tasks.excel_test_task import process_excel_test, test_timed_excel_files
from mygpt.util.notice import notice
from mygpt.util.utils_stopword import filter_stopwords
from mygpt.utils import (  # get_query_key_words_ai_language_key,
    check_content_is_rich_text,
    convert_language_code,
    extract_image_from_content,
    get_analyzer,
    num_tokens_for_langchain_messages,
    num_tokens_from_string,
    replace_image_url_to_uuid,
    replace_md_uuid_images,
    upload_file_to_s3,
    format_sensitive_key,
)
from mygpt.utils_stripe import (
    verify_questions,
)
from mygpt.services.quota_service import QuotaService, QuotaException


router = APIRouter(prefix="/questions", tags=["questions"])
api_router = APIRouter(prefix="/v1")


@trace()
async def pre_question(
    question: QuestionIn,
    ai_obj: Robot,
    user_id: str,
    created_ip,
    created_by,
):
    # 创建一个任务列表
    tasks = [
        save_message_question(
            ai_obj,
            question.session_id,
            user_id,
            question.anonymous_username,
            question.question,
            question.message_id,
            question.page_info,
            created_ip,
            created_by,
            is_test=question.is_test,
        ),
        get_chat_history(question.session_id, 3000),
    ]

    # 使用 asyncio.gather 运行所有任务
    return await asyncio.gather(*tasks)


@trace()
async def analyze_question(
    question: QuestionIn,
    ai_obj: Robot,
    chat_history_str: str = None,
    user_id: str = None,
):
    ai_lang = ai_obj.get_config(AIConfigType.SOURCE_LANG)
    if not ai_lang:
        ai_lang = "en"
    resp = None
    user_intent = UserIntent.ASK_INFO_ABOUT_COMPANY.value
    query_key_words = question.question
    response_language = ai_lang
    message_tokens = 0

    start_time = time.time()
    try:
        resp, message_tokens = await user_intent_detect(
            session_id=question.session_id,
            question=question.question,
            chat_history_str=chat_history_str,
            dictionaries=ai_obj.dictionaries(question.question),
            ai_language=ai_lang,
            user_id=user_id,
            ai_id=str(ai_obj.id),
        )
    except Exception as e:
        logging.error(f"fail to explain question in error: {e}, response json:{resp}")
        sentry_sdk_capture_exception(e)
        user_intent = UserIntent.ASK_INFO_ABOUT_COMPANY.value
        query_key_words = question.question
        response_language = ai_lang
    finally:
        logging.info(
            f"【{question.message_id}】Handle question cost time: {time.time() - start_time} seconds"
        )

    if not resp:
        logging.warning(
            f"【{question.message_id}】error to detect user intent, ai:{ai_obj.id} question:{question}"
        )
    else:
        response_language = resp.get("question_language", None)
        user_intent = resp.get("user_intent", None)
        if not user_intent:
            logging.warning(
                f"【{question.message_id}】user_intent is None, ai:{ai_obj.id} question:{question}"
            )
        elif user_intent == UserIntent.ASK_INFO_ABOUT_BOT.value:
            pass
        else:
            if user_intent != UserIntent.ASK_INFO_ABOUT_COMPANY.value:
                logging.warning(f"unknown user intent, response:{resp}")
                user_intent = UserIntent.ASK_INFO_ABOUT_COMPANY.value

            query_key_words = resp.get("query_key_words", None)
            if not query_key_words:
                query_key_words = question.question

    logging.info(
        f"【{question.message_id}】ai:{ai_obj.id} detect question:{question.question}, ai_lang:{ai_lang}, response_language:{response_language}, query_key_words:{query_key_words}, resp: {resp}"
    )
    return (
        user_intent,
        response_language,
        query_key_words,
        message_tokens,
    )


@trace()
async def search_question_context(
    ai_obj: Robot,
    query: str,
    response_language: str,
    query_key_words: Optional[str],
    vector_collection_filter: Optional[List[VectorCollectionFilter]] = None,
):
    # 4. get background knowledge
    embeddings_model_type = ai_obj.get_config(AIConfigType.EMBEDDINGS_MODEL)
    query_faq_answer_with_embeddings = (
        ai_obj.get_config(AIConfigType.FAQ_SEARCH_ANSWER_WITH_EMBEDDING) == "True"
    )
    embeddings_model = EMBEDDINGS_MODEL.OPENAI
    score = 0.7
    if embeddings_model_type == EMBEDDINGS_MODEL.SENTENCE_TRANSFORMERS:
        embeddings_model = EMBEDDINGS_MODEL.SENTENCE_TRANSFORMERS
        score = 0.2

    embedding_model_name_str = ai_obj.get_config(AIConfigType.EMBEDDING_MODEL_NAME)
    embedding_dimensions = int(
        ai_obj.get_config(AIConfigType.EMBEDDING_DIMENSIONS) or 0
    )

    if ai_obj.get_config(AIConfigType.OPENAI_MODEL) == "":
        openai_model = OpenAIModel.GPT_4_OMNI
    else:
        openai_model = OpenAIModel.to_model(
            ai_obj.get_config(AIConfigType.OPENAI_MODEL)
        )

    options = ContextQueryOptions(
        ai_id=str(ai_obj.id),
        query=query,
        response_language=response_language,
        query_key_words=query_key_words,
        embeddings_model=embeddings_model,
        count=8,
        score=score,
        query_faq_answer_with_embeddings=query_faq_answer_with_embeddings,
        openai_model=openai_model,
        vector_collection_filter=vector_collection_filter,
        enable_rerank=ai_obj.get_config(AIConfigType.ENABLE_RERANK) != "False",
        enable_opensearch=ai_obj.get_config(AIConfigType.ENABLE_OPENSEARCH) != "False",
        embedding_model_name=(
            OpenAIModel(embedding_model_name_str)
            if embedding_model_name_str
            else OpenAIModel.TEXT_EMBEDDING_ADA_002
        ),
        embedding_dimensions=embedding_dimensions,
    )

    return await chunks_query(options=options, ai_obj=ai_obj)


def google_custom_search(query: str):
    service = build("customsearch", "v1", developerKey=GOOGLE_CUSTOM_SEARCH_API_KEY)
    res = (
        service.cse()
        .list(
            q=query,
            cx="810b51471a24d4850",
        )
        .execute()
    )
    return res


async def filename_in_datasets(robot_id: str, filename: str) -> bool:
    robot = await Robot.get(id=robot_id)
    datasets = await robot.datasets.all()

    for dataset in datasets:
        vector_files = await VectorFile.filter(dataset=dataset.id).all()
        if any(vector_file.filename == filename for vector_file in vector_files):
            return True
    return False


co = (
    AsyncClientSession(COHERE_API_KEY, timeout=3, max_retries=3)
    if COHERE_API_KEY
    else None
)


@trace()
async def async_web_search(ai_obj: Robot, query_key_words: str, question: str):
    s_time = time.time()
    try:
        # site:wikipedia.org 新中国 成立 时间 OR 新中国成立时间
        query_str = f"{query_key_words} OR {question}"
        if ai_obj.get_config(AIConfigType.ENABLE_WEB_SEARCH).lower() != "true":
            return []
        else:
            site_list = json.loads(ai_obj.get_config(AIConfigType.WEB_SEARCH_SITE_LIST))
            if len(site_list) == 0:
                search_query = query_str
            else:
                search_query = f"site:{site_list[0]} {query_str}"
        logging.info(
            "web search: generate query time: {}, query: {}".format(
                time.time() - s_time, search_query
            )
        )
        s_time = time.time()
        res = google_custom_search(search_query)
        logging.info("search time: {}".format(time.time() - s_time))
        if "items" not in res:
            return []
        page_links = [item["link"] for item in res["items"]]
        logging.info(
            f"web search Google results: {page_links}, query: {search_query}, qk: {query_key_words}, o_q: {question}, qs: {query_str}"
        )
        page_link = page_links[0]
        s_time = time.time()
        is_in = await filename_in_datasets(ai_obj.id, page_link)
        logging.info("check is_in time: {}".format(time.time() - s_time))
        if is_in:
            return []
        s_time = time.time()
        site_crawler = SiteCrawler(page_link)
        async with aiohttp.ClientSession() as session:
            doc = await site_crawler.parse(page_link, max_images=0, client=session)
            logging.info(
                "web search crawl and parse html time: {}".format(time.time() - s_time)
            )
            if not doc:
                logging.error(
                    "[web search] fail to get html from url: {}".format(page_link)
                )
                return []
            metadata = doc.metadata
            title = metadata.get("title", "")
            description = metadata.get("description")
            file_id: str = metadata.get("parser_file_id")
            del metadata["parser_file_id"]
            data = await split_by_file(file_id, client=session)
        embeddings = []
        doc_id = str(uuid.uuid4())
        for index, chunk in enumerate(data):
            embeddings.append(
                Embeddings(
                    id=str(uuid.uuid4()),
                    text=chunk.page_content,
                    metadata={
                        "title": title + " (From Google Search)",
                        "description": description,
                        "doc_id": doc_id,
                        "chunk_index": index,
                        "source": page_link,
                    },
                )
            )
        docs = []
        for e in embeddings:
            docs.append(
                {
                    "id": e.id,
                    "text": e.text,
                    "metadata": e.metadata,
                }
            )
        if all(not item["text"] for item in docs):
            logging.warning(f"web search: no text in docs, return empty")
            return []
        rerank_res = await co.rerank(
            query=query_key_words,
            documents=docs,
            model="rerank-multilingual-v2.0",
        )
        max_score = 0
        for e in embeddings:
            for result in rerank_res.results:
                if result.document["id"] == e.id:
                    e.score = result.relevance_score
                    max_score = max(max_score, result.relevance_score)
        embeddings.sort(key=lambda x: x.score, reverse=True)
        return_embeddings = []
        tc_sum = 0
        for e in embeddings:
            if tc_sum > 3500:
                break
            tc_sum += num_tokens_from_string(e.text)
            return_embeddings.append(copy.deepcopy(e))
        # return_embeddings = copy.deepcopy(embeddings[:10])
        for e in return_embeddings:
            e.score = max_score
        tcl = []
        tc = 0
        for e in return_embeddings:
            tc1 = num_tokens_from_string(e.text)
            tc += tc1
            tcl.append(tc1)
        return return_embeddings
    except Exception as e:
        logging.error(f"web search fail to async_web_search, error: {e}")
        sentry_sdk_capture_exception(e)
        return []


def get_fallback_final_prompt(
    openai_model: OpenAIModel = None,
    chat_with_image=False,
):
    if openai_model == OpenAIModel.GPT_4_TURBO_PREVIEW:
        system_prompt = (
            gpt_4_turbo_fallback_with_general_knowledge_final_question_prompts["system"]
        )
        user_prompt = (
            gpt_4_turbo_fallback_with_general_knowledge_final_question_prompts["user"]
        )
    else:
        system_prompt = fallback_with_general_knowledge_final_question_prompts["system"]
        user_prompt = fallback_with_general_knowledge_final_question_prompts["user"]
    if chat_with_image:
        system_prompt = (
            fallback_with_general_knowledge_final_question_prompts_with_image["system"]
        )
        user_prompt = fallback_with_general_knowledge_final_question_prompts_with_image[
            "user"
        ]
    if openai_model == OpenAIModel.GPT_4_TURBO_PREVIEW and chat_with_image:
        system_prompt = gpt_4_turbo_fallback_with_general_knowledge_with_image_final_question_prompts[
            "system"
        ]
        user_prompt = gpt_4_turbo_fallback_with_general_knowledge_with_image_final_question_prompts[
            "user"
        ]
    return system_prompt, user_prompt


def get_final_question_prompt_template(
    response_language: Optional[str] = None,
    openai_model: OpenAIModel = None,
    chat_with_image=False,
):
    if openai_model.startswith("gpt-3.5"):
        system_prompt = turbo_system_content
        user_prompt = turbo_user_prompt
    else:
        system_prompt = gpt_4_turbo_final_question_prompts["system"]
        user_prompt = gpt_4_turbo_final_question_prompts["user"]

    if chat_with_image:
        system_prompt = gpt_4_turbo_final_question_prompts_with_image["system"]
        user_prompt = gpt_4_turbo_final_question_prompts_with_image["user"]

    user_prompt = user_prompt if response_language else "{question} {environments}"

    return system_prompt, user_prompt


@trace()
async def prepare_messages(
    question: QuestionIn,
    embeddings: list,
    params: FinalQuestionParams,
    chat_with_image=False,
):
    if not params.chat_history:
        params.chat_history = []
    if params.fallback_to_chatgpt:
        system_prompt, user_prompt = get_fallback_final_prompt(
            params.openai_model,
            chat_with_image,
        )
    else:
        system_prompt, user_prompt = get_final_question_prompt_template(
            params.response_language,
            params.openai_model,
            chat_with_image,
        )
    dict_prompt = get_question_dictionary_prompt(params.dictionaries)
    messages = [
        SystemMessage(
            content=system_prompt.format(
                bot_name=params.bot_name,
                subject_name=params.display_subject_name,
                context=f"{dict_prompt}",
                response_language=params.response_language,
                talking_style=get_talking_style(params.talking_style),
                unknown_text=get_unknown_text(params.unknown_text),
                current_time=datetime.datetime.now(datetime.timezone.utc).strftime(
                    "%Y-%m-%d %H:%M:%S %Z"
                ),
            )
        )
    ]
    messages.extend(params.chat_history)
    messages.append(
        HumanMessage(
            content=user_prompt.format(
                question=question.question,
                environments=get_question_environments(question.metadata),
                response_language=params.response_language,
                extra_goal=get_extra_goal(question.prompt_custom_goal),
                talking_style=get_talking_style(params.talking_style),
            )
        )
    )
    # system + history + user message + ai max_token
    message_token = num_tokens_for_langchain_messages(messages)
    calculate_tokens = message_token + question.max_tokens

    context, total_tokens, reference_list, resources = await get_final_chat_context(
        params.openai_model,
        embeddings,
        calculate_tokens,
        params.llm_provider,
        question.with_images,
    )

    # 去除faq干扰的
    new_reference_list = []
    if reference_list:
        for item in reference_list:
            if item["source"] != "GPTBASE_FAQ":
                new_reference_list.append(item)

    reference_list = new_reference_list

    context = f"{dict_prompt}{context}"

    include_image, include_table = check_content_is_rich_text([context])
    use_4_turbo = include_image or include_table
    # 如果包含表格，并且模型不是gpt-4-turbo，并且是fallback_to_chatgpt，需要更换新的prompt内容
    table_recall = (
        include_table
        and params.openai_model != OpenAIModel.GPT_4_TURBO_PREVIEW
        and params.fallback_to_chatgpt
    )
    if (
        params.openai_model == OpenAIModel.GPT_35_TURBO
        or params.openai_model == OpenAIModel.GPT_4
    ) and use_4_turbo:
        params.openai_model = OpenAIModel.GPT_4_TURBO_PREVIEW
    # 如果包含图片，并且非chat_with_image，需要更换新的prompt内容
    image_recall = include_image and not chat_with_image
    # 在上下文匹配到表格或者图片，需要更换新的prompt内容
    if (table_recall or image_recall) and not chat_with_image:
        return await prepare_messages(
            question,
            embeddings,
            params,
            chat_with_image=True,
        )

    logging.info(
        f"include_image:{include_image}, include_table: {include_table}, \
            chat_with_image: {chat_with_image}, fallback_to_chatgpt: {params.fallback_to_chatgpt}"
    )

    # logging.info(f"reference_list: {reference_list}")

    messages[0].content = system_prompt.format(
        bot_name=params.bot_name,
        subject_name=params.display_subject_name,
        context=context,
        response_language=params.response_language,
        talking_style=get_talking_style(params.talking_style),
        unknown_text=get_unknown_text(params.unknown_text),
        current_time=datetime.datetime.now(datetime.timezone.utc).strftime(
            "%Y-%m-%d %H:%M:%S %Z"
        ),
    )
    return messages, resources, reference_list, total_tokens, params.openai_model


@router.get("/references/{ai_id}", tags=["Get references"])
@api_router.get("/question/references/{ai_id}", tags=["Get references"])
async def get_references(
    message_id: str, api_key: ApiKey = Depends(depend_api_key_with_none)
):
    qa_record = await SessionMessage.get_or_none(id=message_id)

    if qa_record is None:
        raise NotFoundException()
    question = qa_record.question
    answer = qa_record.answer
    reference_list = qa_record.reference_list

    if reference_list is None:
        raise UnsupportedException("No reference_list found")
    if question is None:
        logging.error("Question is None")
        raise UnsupportedException("No question found")

    if answer is None:
        raise UnsupportedException("No answer found")

    reference_map: dict[str, List[dict]] = {}
    title_map: dict[str, str] = {}
    file_id_map: dict[str, str] = {}
    faq_filter_reference_list = []

    for reference in reference_list:
        # 判断是否是faq的
        if "type" in reference and reference["type"] == "faq":
            return await references_faq(reference_list, message_id)
        if "type" in reference and reference["type"] == "vector_faq":
            faq_filter_reference_list.append(reference)
        collection_name = reference.get("dataset_id")
        data_list = reference_map.get(collection_name, [])
        data_list.append(reference)
        reference_map[collection_name] = data_list
        id = reference.get("id")
        title_map[id] = reference.get("title", "")
        file_id = reference.get("file_id", "")
        file_id_map[id] = file_id

    document_messages = ""

    file_id_with_title = set()

    for index, reference in enumerate(reference_list):
        content = ""
        rows = reference.get("content", "").split("\n")
        _has_content = False
        for row in rows:
            text = row.strip()
            match = re.search("\s*\+[-+:]+\+", text)
            if match:
                continue
            match = re.search("^\s*-[\s-]+-$", text)
            if match:
                continue
            text = text.replace("|", " ")
            if not _has_content and row.strip() == "":
                continue
            _has_content = True
            content += f"   {text}\n"
        file_id = reference.get("file_id")
        if file_id and (file_id in file_id_with_title):
            document_messages += turbo_function_call_get_reference_document.format(
                id=reference.get("id"),
                text=content,
                index=index + 1,
            )
        else:
            document_messages += (
                turbo_function_call_get_reference_document_with_title.format(
                    id=reference.get("id"),
                    title=reference.get("title"),
                    text=content,
                    index=index + 1,
                )
            )
            file_id_with_title.add(file_id)
        if index < len(reference_list) - 1:
            document_messages += "----------------------------------------------"
    images = extract_image_from_content(document_messages)
    for index, image in enumerate(images):
        # 替换图片链接为 https://uuid
        uuid = str(index + 1)
        url = image.get("url")
        document_messages = replace_image_url_to_uuid(document_messages, url, uuid)
        question = replace_image_url_to_uuid(question, url, uuid)
        answer = replace_image_url_to_uuid(answer, url, uuid)

    user_prompt = turbo_function_call_get_reference_user.format(
        question=question, answer=answer
    )
    messages = [
        SystemMessage(
            content=turbo_function_call_get_reference_index_prompt.format(
                documents=document_messages
            )
        ),
        HumanMessage(content=user_prompt),
    ]

    system_message_tokens = num_tokens_from_string(messages[0].content)
    user_message_tokens = num_tokens_from_string(messages[1].content)
    total_tokens = system_message_tokens + user_message_tokens

    logging.info(f"get references messages: {messages}")
    reference_ids = []

    session = await qa_record.session
    ai_id = session.robot_id

    openai_model = OpenAIModel.default()

    try:
        max_tokens = 300
        logging.info(f"get ref: model: {openai_model}, messages: {messages}")
        response = await chat_acreate(
            origin_question=user_prompt,
            messages=messages,
            max_tokens=max_tokens,
            openai_model=openai_model,
        )
        content = response.content
        logging.info(f"get reference model response: {content}")
        try:
            document_ids = json.loads(content)
        except Exception as e:
            logging.error(
                "get reference JSON parsing failed, attempting regex extraction",
                exc_info=True,
            )
            matches = re.findall(r"\d+", content)
            document_ids = list(OrderedDict.fromkeys(matches))
            logging.info(f"regex extraction {content=}, {matches=}, {document_ids}")
        # convert to int (on-prem llm may return list[strs])
        document_ids = [int(document_id) for document_id in document_ids]
        logging.info(
            f"get reference with openai_model: {openai_model}, document_ids: {document_ids}"
        )
        embeddings_ids = [embedding.get("id") for embedding in reference_list]

        for doc in document_ids:
            index = doc
            if index > len(embeddings_ids):
                continue
            _id = embeddings_ids[index - 1]
            if _id and _id not in reference_ids and _id in embeddings_ids:
                reference_ids.append({"id": _id})

        file_ids = []
        filter_reference_ids = []
        for index, reference in enumerate(reference_ids):
            id = reference.get("id")
            file_id = file_id_map.get(reference.get("id"), "")
            if file_id:
                if file_id not in file_ids:
                    file_ids.append(file_id)
                else:
                    filter_reference_ids.append(id)
        reference_ids = [
            reference
            for reference in reference_ids
            if reference.get("id") not in filter_reference_ids
        ]

        if len(file_ids) > 0:
            vector_files = await VectorFile.filter(id__in=file_ids)
            for vector_file in vector_files:
                if vector_file.title:
                    for _id, file_id in file_id_map.items():
                        if file_id == vector_file.id:
                            title_map[_id] = vector_file.title
        for reference in reference_ids:
            reference["title"] = title_map.get(reference.get("id"), "")

    except Exception as e:
        logging.error(
            f"retrieve_document_sources error: {e}, traceback: {traceback.format_exc()}"
        )
    if len(faq_filter_reference_list) > 0:
        reference_ids_dict = {
            reference.get("id"): reference for reference in reference_ids
        }
        reference_list_dict = {ref.get("id"): ref for ref in reference_list}
        faq_filter_reference_dict = {}
        for faq in faq_filter_reference_list:
            faq_id = faq.get("faq_id")
            if faq_id not in faq_filter_reference_dict:
                faq_filter_reference_dict[faq_id] = []
            faq_filter_reference_dict[faq_id].append(
                {"id": faq.get("id"), "title": faq.get("title")}
            )

        to_faq_reference_ids = []
        for ref_id, reference in reference_ids_dict.items():
            if ref_id in reference_list_dict:
                _reference = reference_list_dict[ref_id]
                faq_id = _reference.get("faq_id")
                if faq_id and faq_id in faq_filter_reference_dict:
                    faq_references = faq_filter_reference_dict[faq_id]
                    to_faq_reference_ids.extend(faq_references)
                else:
                    to_faq_reference_ids.append(reference)
        qa_record.reference_indexes = to_faq_reference_ids
    else:
        qa_record.reference_indexes = reference_ids
        to_faq_reference_ids = reference_ids
    await qa_record.save()
    return to_faq_reference_ids


@trace()
async def ask_question_received_response(
    event: asyncio.Event,
    message_id: str,
    start_time: float = None,
):
    if not start_time:
        start_time = time.time()

    await event.wait()

    interval_seconds = time.time() - start_time
    logging.info(f"Got first char time after send request: {interval_seconds} seconds")

    if message_id != "":
        await SessionMessage.filter(id=message_id).update(
            first_response_time=interval_seconds * 1000
        )


@trace()
async def create_final_chat_question(
    ai_obj: Robot,
    question: QuestionIn,
    question_record_obj: SessionMessage,
    embeddings: list,
    response_language: Optional[str] = None,
    chat_history: Optional[list] = None,
    event: Optional[asyncio.Event] = None,
    recommend_list: List[dict] = None,
    faq_child_node_list: List[dict] = None,
):
    use_gpt_4 = ai_obj.get_config(AIConfigType.FINAL_QUESTION_USE_GPT4) == "True"
    model = ai_obj.get_config(AIConfigType.OPENAI_MODEL)
    logging.info(f"create_final_chat_question, model: {model}, ai_obj: {ai_obj}")
    if not model:
        # old gpt-4 conf
        model = "gpt_4" if use_gpt_4 else "gpt_4o"
    openai_model = OpenAIModel.to_model(model)

    logging.info(f"bot: {ai_obj.id}, final openai_model: {openai_model}")
    fallback_to_chatgpt = (
        ai_obj.get_config(AIConfigType.FALLBACK_TO_CHATGPT).lower() == "true"
    )
    llm_provider = ai_obj.get_config(AIConfigType.LLM_PROVIDER)
    talking_style = ai_obj.get_config(AIConfigType.TALKING_STYLE)
    chat_temperature = None
    try:
        chat_temperature = ai_obj.get_config(AIConfigType.CHAT_TEMPREATURE)
        temperature = float(chat_temperature) if chat_temperature else 0
    except Exception as e:
        logging.warning(f"fail to get chat_temperature:{chat_temperature}, error: {e}")
        temperature = 0
    unknown_text = ai_obj.get_config(AIConfigType.UNKNOWN_TEXT)

    params = FinalQuestionParams(
        bot_name=ai_obj.name,
        display_subject_name=ai_obj.display_subject_name,
        unknown_text=unknown_text,
        response_language=response_language,
        chat_history=chat_history,
        openai_model=openai_model,
        llm_provider=llm_provider,
        fallback_to_chatgpt=fallback_to_chatgpt,
        dictionaries=ai_obj.dictionaries(question.question),
        talking_style=talking_style,
    )

    (
        messages,
        resources,
        reference_list,
        total_tokens,
        openai_model,
    ) = await prepare_messages(
        question,
        embeddings,
        params,
    )
    question_record_obj.reference_list = reference_list
    question_record_obj.total_tokens = total_tokens
    question_record_obj.model = openai_model
    logging.info(
        f"use final_question:{repr(messages[-1].content)} ask ai: {ai_obj.id}, final_question_use_gpt_4: {use_gpt_4}, openai_model: {openai_model} in messages: {repr(messages)}"
    )

    none_content_reference_list = []
    for reference in question_record_obj.reference_list:
        none_content_reference_list.append({**reference, "content": ""})
    resp = await chat_acreate(
        origin_question=question.question,
        messages=messages,
        temperature=temperature,
        max_tokens=question.max_tokens,
        message_id=question.message_id,
        session_id=question.session_id,
        stream=question.stream,
        reference_list=none_content_reference_list,
        resources=resources,
        format=question.format,
        final_question_use_gpt_4=use_gpt_4,
        openai_model=openai_model,
        event=event,
        llm_provider=llm_provider,
        recommend_list=recommend_list,
        faq_child_node_list=faq_child_node_list,
    )

    question_record_obj.contexts = [message.content for message in messages]
    question_record_obj.comes_from = MESSAGE_COMES_FROM.CHUNK.value
    if question.debug_with_messages:
        new_messages = copy.deepcopy(messages)
        image_resources = resources.get("image", {}) if resources else None
        if image_resources:
            for message in new_messages:
                message.content = replace_md_uuid_images(
                    message.content, image_resources
                )
        question_record_obj.answer = resp.content
    return resp, question_record_obj, messages


@trace()
async def answer_digital_human(
    ai_obj: Robot,
    question: QuestionIn,
    event: asyncio.Event,
    user_id: str,
    question_record_obj,
):
    history, count = await get_chat_history_turbo(question.session_id, 2)
    prompt = digital_human_robot_default_prompt
    # if ai_obj.get_config(AIConfigType.DIGITAL_HUMAN_PROMPT):
    #     prompt = ai_obj.get_config
    if ai_obj.prompt:
        prompt = ai_obj.prompt
    messages = [
        SystemMessage(content=prompt),
        *history,
        HumanMessage(content=question.question),
    ]

    oai_model = OpenAIModel.to_model(ai_obj.get_config(AIConfigType.OPENAI_MODEL))

    resp = await chat_acreate(
        origin_question=question.question,
        messages=messages,
        max_tokens=question.max_tokens,
        message_id=question.message_id,
        session_id=question.session_id,
        stream=question.stream,
        event=event,
        openai_model=oai_model,
    )

    if question.stream:
        return StreamingResponse(
            resp,
            media_type="text/event-stream",
        )
    elif question.debug_with_messages:
        return QuestionOut(answer=resp.content, messages=messages)
    else:
        question_record_obj.answer = resp.content
        return QuestionOut(answer=resp.content)


# from mygpt.agent_exp.agent_factory import AgentFactory
# from mygpt.agent_exp.utils.using_util import covert_mod_messages
# from mygpt.agent_exp.service_handler import agent_reply_postprocess_handler
#
#
# async def answer_agent(
#         robot: Robot,
#         question_in: QuestionIn,
#         question_record_obj,
#         event: asyncio.Event,
#         user_id: str,
#         request: Request,
#         verbose: bool = True,
# ):
#     history, count = await get_chat_history_turbo(question_in.session_id, 6)
#     chat_history = covert_mod_messages(history)
#     if question_in.with_images is None:
#         question_in.with_images = robot.get_config(AIConfigType.ENABLE_IMAGES).lower() == "true"
#     env = {
#         "robot": robot,
#         "question_in": question_in,
#         "question_record_obj": question_record_obj,
#         "chat_history": chat_history,
#         "event": event,
#         "user_id": user_id,
#         "request": request,
#     }
#     agent = AgentFactory.create_agent(robot, question_in=question_in, chat_history=chat_history, verbose=verbose)
#     reply, thought_step_count = await agent.run(question_in, chat_history, env=env)
#     logging.info(f"【{question_in.message_id}】agent reply: {reply}, thought_step_count: {thought_step_count}")
#     resp = env.get("resp")
#     if reply:
#         # reply 不为空, 说明是正常的字符串, 需要组件resp
#         # 判断reply类型
#         if reply == "<|||bos|||>resp<|||eos|||>":
#             # reply以标记形式返回, 最终结果需要获取resp对象中的content.
#             # reply以标记形式返回, 数据库等后处理已经在工具内完成.
#             return resp
#         # 处理数据写库等操作
#         asyncio.create_task(agent_reply_postprocess_handler(question_in, reply, chat_history))
#         return reply
#     else:
#         # reply 为空, 需要获取env中的resp对象, 直接返回即可
#         # 流式情况下, reply为空, 写库操作在工具内部进行处理. (因为需要快速与现有的RAG进行快速整合, 写库的操作在已有的RAG内部完成)
#         resp = env.get("resp")
#         return resp


from mygpt.error import UnauthorizedException
import json


async def check_admin_or_collaborator_access(
    ai_id: UUID,
    user_id: str,
) -> bool:
    """
    验证是否是AI的管理员或协助者
    如果是本地运行，返回True
    """
    robot_obj = await Robot.get_or_none(id=ai_id, deleted_at__isnull=True)
    if robot_obj and robot_obj.user_id == user_id:
        # admin
        return True
    # assist
    exists = await AccountMember.filter(
        member_id=user_id,
        resource_type=ResourceType.ROBOT,
        deleted_at__isnull=True,
        resource_id=ai_id,
    ).exists()
    return exists


@router.post("/{ai_id}", response_model=QuestionOut)
@api_router.post("/question/{ai_id}", response_model=QuestionOut, tags=["Question"])
async def ask_question(
    ai_id: UUID,
    question: QuestionIn,
    request: Request,
    ai_obj: Robot = Depends(verify_questions),
    user: Optional[Auth0User] = Security(get_current_oauth_user_with_gbase_user),
    api_key: ApiKey = Depends(depend_api_key_with_none),
):
    """
    When you upload a file through the file upload interface,
    you can use this interface to ask questions about the content of the file you upload,
    and this interface will return the retrieved answer.
    """
    logging.info(
        f"ask question, bot: {ai_id}, question: {question}, bot created at: {ai_obj.created_at}"
    )
    question_len = len(question.question)
    if question_len > 16 * 1024:
        logging.warning(
            f"[ask_question] question too long, len: {question_len}, question: {question.question}"
        )
        raise InvalidParameterException(
            f"question too long, len: {question_len}, question: {question.question}"
        )

    # 检查时间限制
    questions_start_date = ai_obj.get_config(AIConfigType.QUESTIONS_START_DATE)
    questions_end_date = ai_obj.get_config(AIConfigType.QUESTIONS_END_DATE)

    if questions_start_date or questions_end_date:
        current_time = datetime.datetime.now(datetime.timezone.utc)

        # 检查开始时间限制
        if questions_start_date:
            try:
                start_date = datetime.datetime.fromisoformat(
                    questions_start_date.replace("Z", "+00:00")
                )
                if current_time < start_date:
                    raise InvalidParameterException(
                        "Unable to send messages during this time period according to bot administrator configuration"
                    )
            except ValueError:
                logging.warning(f"Invalid start date format: {questions_start_date}")

        # 检查结束时间限制
        if questions_end_date:
            try:
                end_date = datetime.datetime.fromisoformat(
                    questions_end_date.replace("Z", "+00:00")
                )
                if current_time > end_date:
                    raise InvalidParameterException(
                        "根据 bot 管理员的配置，这个时间段无法发送消息"
                    )
            except ValueError:
                logging.warning(f"Invalid end date format: {questions_end_date}")
    start_time = time.time()
    user_id = api_key.user_id if api_key else None
    if not user_id and user:
        user_id = user.id

    # 检查消息配额
    # 获取机器人所有者
    user = await ai_obj.user
    bot_owner_id = user.id

    # 将 bot_owner_id 保存到一个非局部变量中，以便在 finally_operation 中使用
    ai_obj.bot_owner_id = bot_owner_id
    if QUOTA_CHECK and bot_owner_id:
        from mygpt.models import UsageType

        try:
            # 检查机器人所有者是否有足够的消息配额
            has_quota = await QuotaService.check_quota(
                user_id=str(bot_owner_id), resource_type=UsageType.MESSAGE_SENT
            )

            if not has_quota:
                # 获取配额详情以显示更有用的错误消息
                available, total, used = await QuotaService.get_effective_quota(
                    user_id=str(bot_owner_id), resource_type=UsageType.MESSAGE_SENT
                )
                quota_message = f"Message quota exceeded."
                logging.warning(
                    f"Bot owner {bot_owner_id} has exceeded their message quota. Available: {available}, Total: {total}, Used: {used}"
                )
                raise QuotaException(
                    detail=f"{quota_message} Please contact the bot owner to upgrade their plan.",
                    status_code=402,  # Payment Required
                )

            logging.info(
                f"Bot owner {bot_owner_id} message quota check passed. Proceeding with question."
            )

        except QuotaException:
            # 重新抛出配额异常
            raise
        except Exception as e:
            # 记录其他异常但不阻止请求
            logging.error(
                f"Error checking message quota: {e}, {traceback.format_exc()}"
            )
            sentry_sdk_capture_exception(e)

    robot_message_quota_str = ai_obj.get_config(AIConfigType.MAX_QUESTIONS)
    if robot_message_quota_str != "":
        # check bot message quota
        try:
            robot_message_quota = int(robot_message_quota_str)
        except ValueError as e:
            logging.error(
                f"Invalid value for MAX_QUESTIONS: {robot_message_quota_str} for bot {ai_id}"
            )
            sentry_sdk_capture_exception(e)
            robot_message_quota = None
        if robot_message_quota < 0:
            logging.warning(
                f"unexcepted value for robot_message_quota: {robot_message_quota_str}"
            )
            robot_message_quota = None
        err_msg = f"Bot {ai_id} has reached its message quota. Please contact the bot owner to upgrade their configuration."
        if robot_message_quota is None:
            pass
        elif robot_message_quota == 0:
            logging.warning(err_msg)
            raise QuotaException(
                detail=err_msg,
                status_code=402,
            )
        else:
            # 检查消息配额
            bot_quota_start_time = ai_obj.get_config(AIConfigType.QUESTIONS_START_DATE)
            bot_quota_end_time = ai_obj.get_config(AIConfigType.QUESTIONS_END_DATE)
            try:
                # 解析开始和结束日期
                start_date = (
                    datetime.datetime.fromisoformat(
                        bot_quota_start_time.replace("Z", "+00:00")
                    )
                    if bot_quota_start_time
                    else None
                )
                end_date = (
                    datetime.datetime.fromisoformat(
                        bot_quota_end_time.replace("Z", "+00:00")
                    )
                    if bot_quota_end_time
                    else None
                )

                # 统计该机器人在指定时间范围内的消息数量
                qs = SessionMessage.filter(session__robot_id=ai_id)
                if start_date:
                    qs = qs.filter(created_at__gte=start_date)
                if end_date:
                    qs = qs.filter(created_at__lte=end_date)
                message_count = await qs.count()

                if message_count >= robot_message_quota:
                    logging.warning(f"Bot {ai_id} has reached its message quota.")
                    raise QuotaException(
                        detail="This bot's dedicated message quota has been used up.",
                        status_code=402,
                    )
            except Exception as e:
                logging.error(f"Error checking message quota: {e}")
                sentry_sdk_capture_exception(e)

    # 添加访问控制检查
    enable_access_control_value = ai_obj.get_config(AIConfigType.ENABLE_ACCESS_CONTROL)

    enable_access_control = (
        enable_access_control_value.lower() == "true"
        if enable_access_control_value
        else False
    )
    if enable_access_control:
        if not user_id:
            raise UnauthorizedException(
                "Access Denied: You must be logged in to access this API."
            )

        # 检查用户是否为bot admin或协助者
        is_admin_or_collaborator = await check_admin_or_collaborator_access(
            ai_id, user_id
        )
        if not is_admin_or_collaborator:
            allow_email_list = ai_obj.get_config(
                AIConfigType.ACCESS_CONTROL_ALLOW_EMAIL_LIST
            )
            if allow_email_list:
                allow_emails = json.loads(allow_email_list)
                user_email = (
                    user.email
                    if user
                    else (getattr(api_key, "email", None) if api_key else None)
                )
                if user_email:
                    try:
                        # 规范化用户的电子邮件地址
                        normalized_user_email = validate_email(
                            user_email, check_deliverability=False
                        ).normalized
                    except EmailNotValidError:
                        logging.error(f"Invalid user email address: {user_email}")
                        raise UnauthorizedException("Invalid user email address")

                    if normalized_user_email not in allow_emails:
                        logging.warning(
                            f"Unauthorized access attempt: {normalized_user_email} not in allow list"
                        )
                        raise UnauthorizedException(
                            "Access Denied: You are not authorized to access this API."
                        )
                else:
                    logging.error(
                        f"User email is missing for access control check, u: {user}, ak: {api_key}"
                    )
                    raise UnauthorizedException(
                        "Access Denied: Your email address is required for access control but could not be retrieved."
                    )

    if not question.question or question.question.strip() == "":
        raise InvalidParameterException()

    if not question.message_id:
        question.message_id = uuid.uuid4()
    logging.debug(
        f"[message_id]:【{question.message_id}】ask question: {question.question}"
    )
    session_id = question.session_id
    session = await Session.filter(session_id=session_id)
    if session and not any(s.robot_id == ai_id for s in session):
        # 400 bad request
        raise HTTPException(
            status_code=400,
            detail=f"session_id {session_id} used by other bot, please choose another session_id",
        )

    # 根据用户输入的提示词中如果包含 felo search则直接触发canvas
    q = question.question.upper()
    created_ip = request.client.host
    if request and "x-real-ip" in request.headers:
        created_ip = request.headers["x-real-ip"]

    created_by = question.created_by
    if not created_by:
        created_by = user_id

    question_record_obj, chat_history_str = await pre_question(
        question, ai_obj, user_id, created_ip, created_by
    )

    # 启动异步线程待等event，用于apm统计接收到第1个字符或完整消息的时间，由stream字段决定
    event = asyncio.Event()
    finish_event = asyncio.Event()
    asyncio.create_task(ask_question_received_response(event, str(question.message_id)))
    assert ai_obj.robot_type is not None
    logging.info(f"【{question.message_id}】ai_obj.robot_type: {ai_obj.robot_type}")

    # 添加一个标志来跟踪请求是否成功完成
    request_successful = False

    try:
        # 如果确定走FAQ, 则快速返回答案.
        if question.recommend_faq_id:
            resp = await fast_faq(
                ai_obj=ai_obj,
                question_in=question,
                event=event,
                user_id=user_id,
                question_record_obj=question_record_obj,
                chat_history_str=chat_history_str,
            )
            finish_event.set()
            request_successful = True  # 标记请求成功
            if isinstance(resp, StreamingResponse) or isinstance(resp, QuestionOut):
                return resp
            if question.stream:
                return StreamingResponse(
                    resp,
                    media_type="text/event-stream",
                )
            else:
                return QuestionOut(answer=resp)
        if ai_obj.robot_type == RobotType.DIGITAL_HUMAN:
            resp = await answer_digital_human(
                ai_obj, question, event, user_id, question_record_obj
            )
            request_successful = True  # 标记请求成功
            return resp
        if ai_obj.robot_type == RobotType.AGENT:
            history, count = await get_chat_history_turbo(question.session_id, 20)
            base_model = (
                question.model
                if question.model
                else ai_obj.get_config(AIConfigType.OPENAI_MODEL)
            )
            logging.info(
                f"create_final_chat_question, model: {base_model}, ai_obj: {ai_obj}"
            )
            if not base_model:
                base_model = OpenAIModel.GPT_4_OMNI_2024_11_20
                logging.info(
                    f"ai_obj.get_config(AIConfigType.OPENAI_MODEL) is None, use default model: {base_model}"
                )
            else:
                base_model = OpenAIModel.to_model(base_model)
            # base_model = OpenAIModel.GPT_4_OMNI_2024_11_20
            if question.agent_mode:
                prompt_type = PromptType.to_type(question.agent_mode)
            else:
                prompt_type = PromptType.default()
            logging.info(
                f"[ask] agent 模式 - 原始值: {question.agent_mode}, 转换后: {prompt_type}"
            )
            logging.info(
                f"[ask_question] agent 模式 - language 参数: {question.language}"
            )

            afc_engine = AgentFunctionCallEngine(
                question_in=question,
                robot=ai_obj,
                chat_history=history,
                base_model=base_model,
                start_event=event,
                finish_event=finish_event,
                chat_history_str=chat_history_str,
                question_record_obj=question_record_obj,
                prompt_type=prompt_type,
            )
            language = None
            if question.language:
                language = convert_language_code(question.language)
            response = await afc_engine.run(language=language)
            function_call_sta = afc_engine.function_call_sta
            history_conversation_count = 0
            recommend_list = []
            faq_child_node_list = []
            if question.stream:
                resp = response.gpt_response_iter
                if not resp:
                    resp = prepare_answer2chatchunk(
                        response.content, question.message_id
                    )
                request_successful = True  # 标记请求成功
                return StreamingResponse(
                    resp,
                    media_type="text/event-stream",
                )
            else:
                resp = response.content
                if isinstance(resp, str):
                    request_successful = True  # 标记请求成功
                    return QuestionOut(answer=resp)
                elif isinstance(resp, AsyncIterator):
                    request_successful = True  # 标记请求成功
                    return StreamingResponse(
                        resp,
                        media_type="text/event-stream",
                    )
        # 正常走RAG模式
        resp = await answer_rag_bot(
            user_id=user_id,
            ai_obj=ai_obj,
            question_in=question,
            question_record_obj=question_record_obj,
            chat_history_str=chat_history_str,
            event=event,
        )
        request_successful = True  # 标记请求成功
        return resp
    except openai.BadRequestError as e:
        request_successful = False
        if not event.is_set():
            event.set()
        sentry_sdk_capture_exception(e)

        logging.error(traceback.format_exc())  # Log the stacktrace
        await notice("BadRequestError create_question -> error 1: " + str(e))
        logging.error(
            f"fail to create_question {question} in ai:{ai_id} and error 1: {e}"
        )
        raise HTTPException(status_code=400, detail=str(e))
    except openai.APITimeoutError as e:
        request_successful = False
        if not event.is_set():
            event.set()
        sentry_sdk_capture_exception(e)

        logging.error(
            f"fail to create_question {question} in  ai:{ai_id} and error 2: {e}"
        )
        await notice("APITimeoutError create_question -> error 2: " + str(e))
        raise HTTPException(status_code=400, detail="Request Openai Api timed out")

    except Exception as e:
        request_successful = False
        if not event.is_set():
            event.set()
        sentry_sdk_capture_exception(e)

        # 获取当前使用的模型和提供商信息
        model_info = ""
        try:
            if hasattr(question_record_obj, "model") and question_record_obj.model:
                model_info = f", model: {question_record_obj.model}"
            api_key_info = format_sensitive_key(api_key)
            model_info = f"{model_info} {api_key_info}"
        except Exception as model_ex:
            logging.error(f"获取模型信息失败: {model_ex}")
            model_info = ", 获取模型信息失败"
        logging.error(
            f"fail to create_question {question} in  ai:{ai_id} and error 3: {e}{model_info}"
        )
        await notice(f"create_question -> error 3:{str(e)}{model_info}")

        logging.error(traceback.format_exc())  # Log the stacktrace
        raise HTTPException(
            status_code=500,
            detail="Sorry, something went wrong.Please try again later.",
        )
    finally:

        async def finally_operation():
            # 保证最终操作一定会执行
            try:
                if ai_obj.robot_type == RobotType.AGENT:
                    await asyncio.wait_for(finish_event.wait(), timeout=600)
                else:
                    finish_event.set()
            except asyncio.TimeoutError:
                logging.error("[ask_question] finish_event timeout")
                logging.info(f"finish_event timeout - question: {question.question}")
            asyncio.create_task(
                save_session_title(question.session_id, question_record_obj)
            )
            # asyncio.create_task(incr_user_and_robot_question_usage_count(ai_obj))

            # 只有在请求成功时才消费消息配额
            if QUOTA_CHECK and hasattr(ai_obj, "bot_owner_id"):
                if not request_successful:
                    logging.info(
                        f"Skipping quota consumption for bot owner {ai_obj.bot_owner_id} due to failed request"
                    )
                else:
                    try:
                        from mygpt.models import UsageType
                        from mygpt.services.quota_service import QuotaService

                        # 消费一个消息配额
                        asyncio.create_task(
                            QuotaService.consume_quota(
                                user_id=str(ai_obj.bot_owner_id),
                                resource_type=UsageType.MESSAGE_SENT,
                                amount_to_consume=1,
                            )
                        )
                        logging.info(
                            f"Consumed one message quota for bot owner {ai_obj.bot_owner_id}"
                        )
                    except Exception as e:
                        # 记录错误但不阻止操作
                        logging.error(f"Error consuming message quota: {e}")
                        sentry_sdk_capture_exception(e)
            if (
                not question.stream
            ) and question_record_obj.comes_from != MESSAGE_COMES_FROM.FAQ:
                # question_record_obj.status = SessionMessageStatus.FINISHED
                # await SessionEvaluate.add_message_count(question_record_obj.session_id)
                asyncio.create_task(
                    save_to_quality_assessment_by_message_id(question_record_obj.id)
                )
                logging.info("not stream, save question_record_obj, add message count")
            success = await save_message_with_status_check(question_record_obj)
            if not success:
                logging.warning(f"Failed to save message {question_record_obj.id}")
            interval_seconds = time.time() - start_time
            logging.info(
                f"Stream task create after send request: {interval_seconds} seconds"
            )

        asyncio.create_task(finally_operation())


async def save_message_with_status_check(question_record_obj) -> bool:
    """
    保存消息记录，并根据数据库中的状态更新当前对象的状态
    Args:
        question_record_obj: 需要保存的消息记录对象
    Returns:
        bool: 保存操作是否成功
    """
    if not question_record_obj or not question_record_obj.id:
        logging.error("Invalid question record object")
        return False
    logging.info(f"准备保存: {question_record_obj.id}")
    has_lock = False
    try:
        # 获取数据库连接
        async with in_transaction() as connection:
            # 检查数据库中的状态
            db_question_record = (
                await SessionMessage.filter(id=question_record_obj.id)
                .select_for_update()
                .using_db(connection)
                .first()
            )

            if not db_question_record:
                logging.error(f"Message not found: {question_record_obj.id}")
                return False

            # 尝试获取锁来更新状态
            has_lock = await acquire_message_lock(question_record_obj.id)
            if has_lock:
                logging.info(f"message locked: {question_record_obj.id}")
                # 有锁时才更新状态
                if (
                    db_question_record.status is None
                    or db_question_record.status == SessionMessageStatus.PENDING
                ):
                    question_record_obj.status = SessionMessageStatus.PENDING
                elif db_question_record.status == SessionMessageStatus.FINISHED:
                    question_record_obj.status = SessionMessageStatus.FINISHED
            else:
                # 没有锁时保持原状态
                question_record_obj.status = db_question_record.status
                logging.info(
                    f"Cannot acquire lock for message {question_record_obj.id}, keeping original status: {db_question_record.status}"
                )

            # 保存整个对象
            await question_record_obj.save()
            logging.info(
                f"Saved message {question_record_obj.id} with status {question_record_obj.status}"
            )

            return True

    except Exception as e:
        logging.error(f"Failed to save message: {str(e)}")
        return False
    finally:
        # 只有在成功获取锁的情况下才需要释放
        if has_lock:
            logging.info(f"message unlocked: {question_record_obj.id}")
            await release_message_lock(question_record_obj.id)


@router.post(
    "/{ai_id}/embeddings.query.v2",
)
async def query_embeddings_v2(
    ai_id: str,
    query: QueryEmbeddingsIn,
):
    analyzer = get_analyzer(query.language)
    options = ContextQueryOptions(
        ai_id=ai_id,
        query=query.prompt,
        analyzer=analyzer,
        count=query.count,
        score=query.score,
        openai_model=query.openai_model,
        query_key_words=query.query_key_words,
    )
    chunks = await chunks_query(options)
    res = []
    for i, chunk in enumerate(chunks):
        res.append({"index": i, "chunk": chunk})
    return res


@router.get(
    "/test/batch-qa/status/{task_id}",
)
async def batch_qa_excel_status(
    task_id: str, user: Auth0User = Security(get_current_oauth_user_with_gbase_user)
):
    """
    Get task status of `task_id`
    """
    # Retrieve the task by task_id and user_id
    try:
        task = await ExcelTestTask.get(id=task_id)
    except DoesNotExist:
        raise HTTPException(
            404,
            "Task not found.",
        )
    logging.info("getting status of %s", task_id)

    # Create a Pydantic model instance from the task
    task_pydantic = await ExcelTestTask_Pydantic.from_tortoise_orm(task)
    # Create a presigned URL for the input and output files, so that user can download them
    # if task_pydantic.input_file_url:
    #     task_pydantic.input_file_url = _create_presigned_url(bucket_name=AWS_S3_BUCKET_NAME,
    #                                                          object_name=task.input_file_url.split('/')[-1])
    # if task_pydantic.output_file_url:
    #     task_pydantic.output_file_url = _create_presigned_url(bucket_name=AWS_S3_BUCKET_NAME,
    #                                                           object_name=task.output_file_url.split('/')[-1])

    # Return the task status as a JSON response
    return task_pydantic


@router.get("/test/timed-test-excel")
async def get_timed_test_excel():
    files = await TimedExcelTestFile.all()
    return files


SUPPORTED_EXCEL_FILE_TYPES = [".xlsx", ".xlsm", ".xltx", ".xltm"]


@router.put("/test/timed-test-excel")
async def add_timed_test_excel(file: UploadFile = File(...), env: str = Form(None)):
    if env not in ["TEST", "PROD"]:
        raise OperationFailedException(
            "Invalid env value. Must be either 'TEST' or 'PROD'."
        )
    filename = file.filename
    file_name, file_extension = os.path.splitext(filename)
    if file_extension not in SUPPORTED_EXCEL_FILE_TYPES:
        raise OperationFailedException("Only Excel files are allowed")

    current_time = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
    unique_file_name = f"{file_name}___{current_time}{file_extension}"

    with open(file.filename, "wb") as buffer:
        buffer.write(file.file.read())

    with open(file.filename, "rb") as f:
        s3_file_url = await upload_file_to_s3(
            unique_file_name,
            f,
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )

    timed_excel_test_file = await TimedExcelTestFile.create(
        name=filename, unique_name=unique_file_name, s3_file_url=s3_file_url, env=env
    )
    message_url = "https://open.larksuite.com/open-apis/bot/v2/hook/380feb82-4743-49f7-b877-68d60eb5494b"
    headers = {"Content-Type": "application/json"}
    text_content = f"""\
测试文件更新
    环境: {env}
    文件名: {timed_excel_test_file.name}
    unique_name: {timed_excel_test_file.unique_name}
    s3_file_url: {timed_excel_test_file.s3_file_url}"""
    text_data = {"msg_type": "text", "content": {"text": text_content}}

    async def send_request(url, headers, data):
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url, headers=headers, data=json.dumps(data)
            ) as response:
                print(await response.text())

    await send_request(url=message_url, headers=headers, data=text_data)
    return timed_excel_test_file


@router.delete("/test/timed-test-excel")
async def delete_timed_test_excel(file_id: str):
    file = await TimedExcelTestFile.get_or_none(id=file_id)
    res = await file.soft_delete()
    return res


@router.post(
    "/test/batch-qa",
)
# @api_router.post(
#     '/test/question/batch-qa',
# )
async def batch_qa_excel(
    file: UploadFile = File(...),
    user: Auth0User = Security(get_current_oauth_user_with_gbase_user),
    api_key: ApiKey = Depends(depend_api_key_with_none),
):
    """
    Batch testing a chatbot with Excel
    Note: If program failed to read file, open and save file with ms excel again before upload the Excel file
    """
    user_id = api_key.user_id if api_key else (user.id if user else None)
    # if not user_id:
    #     raise HTTPException(
    #         status_code=HTTP_403_FORBIDDEN,
    #         detail='Login required.',
    #     )
    logging.info(f"user_id: {user_id}, file: {file}")

    filename, file_extension = os.path.splitext(file.filename)
    logging.info(f"filename: {filename}, file_extension: {file_extension}")
    if file_extension not in SUPPORTED_EXCEL_FILE_TYPES:
        raise OperationFailedException("Only Excel files are allowed")
    with open(file.filename, "wb") as buffer:
        buffer.write(file.file.read())

    excel_test_task = ExcelTestTask(
        user_id=user_id, filename=file.filename, status=ExcelTestTaskStatus.ACCEPTED
    )

    input_filename = f"input_{filename}___{excel_test_task.id}{file_extension}"
    logging.info("input name is: %s", input_filename)
    # Upload the input file to S3
    with open(file.filename, "rb") as f:
        input_file_url = await upload_file_to_s3(
            input_filename,
            f,
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )
        excel_test_task.input_file_url = input_file_url
    os.remove(file.filename)
    await excel_test_task.save()

    # output_s3_filename = f"output_{file.filename}_{excel_test_task.id}.xlsx"

    # Call the Celery task
    process_excel_test.delay(excel_test_task.id)
    logging.info(f"create task: {excel_test_task}")

    # Return the task status and other necessary information in the response
    return JSONResponse(
        content={
            "task_id": str(excel_test_task.id),
        }
    )


# Create a Pydantic model for ExcelTestTask
ExcelTestTask_Pydantic = pydantic_model_creator(
    ExcelTestTask, name="ExcelTestTask", exclude=("user",)
)


def _create_presigned_url(bucket_name, object_name, expiration=30 * 24 * 3600):
    """Generate a presigned URL to share an S3 object

    :param bucket_name: string
    :param object_name: string
    :param expiration: Time in seconds for the presigned URL to remain valid
    :return: Presigned URL as string. If error, returns None.
    """

    # Generate a presigned URL for the S3 object
    logging.info("create_presigned_url")
    s3_client = boto3.client("s3")
    # logging.info('keys [%s] [%s] [%s] [%s]', AWS_ACCESS_KEY_ID,
    #              AWS_SECRET_ACCESS_KEY, AWS_S3_BUCKET_NAME, AWS_S3_REGION)
    logging.info("params [%s] [%s] [%s]", bucket_name, object_name, expiration)
    try:
        response = s3_client.generate_presigned_url(
            "get_object",
            Params={"Bucket": bucket_name, "Key": object_name},
            ExpiresIn=expiration,
        )
    except ClientError as e:
        logging.error(e)
        return None

    # The response contains the presigned URL
    return response


# @api_router.get('/test/question/batch-qa/tasks', response_model=List[ExcelTestTask_Pydantic])
@router.get("/test/batch-qa/tasks", response_model=List[ExcelTestTask_Pydantic])
async def list_batch_qa_tasks(
    user: Auth0User = Security(get_current_oauth_user_with_gbase_user),
):
    """
    return all tasks (maybe for this user)
    """
    # Check if the user is authenticated
    # if not user:
    #     raise HTTPException(
    #         status_code=HTTP_403_FORBIDDEN,
    #         detail='Login required.',
    #     )

    # # Query the database for all tasks, sorted by 'create_at' in descending order
    # tasks = await ExcelTestTask.filter(user_id=user.id).order_by('-created_at').all()
    tasks = await ExcelTestTask.all().order_by("-created_at").all()
    logging.debug("tasks: %s, size: %s", tasks, len(tasks))
    # following take too long, not use for now
    # for task_pydantic in tasks:
    #     # Create a presigned URL for the input and output files, so that user can download them
    #     if task_pydantic.input_file_url:
    #         task_pydantic.input_file_url = _create_presigned_url(bucket_name=AWS_S3_BUCKET_NAME,
    #                                                              object_name=task_pydantic.input_file_url.split('/')[-1])
    #     if task_pydantic.output_file_url:
    #         task_pydantic.output_file_url = _create_presigned_url(bucket_name=AWS_S3_BUCKET_NAME,
    #                                                               object_name=task_pydantic.output_file_url.split('/')[-1])

    tasks_pydantic = [
        await ExcelTestTask_Pydantic.from_tortoise_orm(task) for task in tasks
    ]

    # Return the list of tasks as a JSON response
    return tasks_pydantic


# # Return the list of tasks as a JSON response
# return await ExcelTestTask_Pydantic.from_queryset_async(tasks)
@router.put(
    "/{ai_id}/session/{session_id}/evaluate",
    tags=["Question"],
    status_code=HTTP_204_NO_CONTENT,
)
async def session_evaluate(
    ai_id: str,
    session_id: str,
    score: float = Body(..., gt=0, lt=6),
    user: Optional[Auth0User] = Security(get_current_oauth_user_with_gbase_user),
    api_key: ApiKey = Depends(depend_api_key_with_none),
):
    session_obj = await Session.get_or_none(
        robot_id=ai_id, session_id=session_id, deleted_at__isnull=True
    ).prefetch_related("session_evaluate")
    if not session_obj:
        raise NotFoundException("Session not found")

    session_evaluate_obj = session_obj.session_evaluate
    if session_evaluate_obj.score == 0:
        await create_or_update_session_score(ai_id, 1, score)
    session_evaluate_obj.score = score
    await session_evaluate_obj.save()

    return session_obj


@router.post(
    "/{ai_id}/session/messages/group_by_day",
    dependencies=[Depends(verify_admin_access)],
)
async def get_session_message_group_by_day(
    ai_id: str,
    user_id: Optional[str] = None,
    start_time: datetime.datetime = None,
    end_time: datetime.datetime = None,
    rating: Optional[int] = None,
    only_rating: bool = False,
    include_test: bool = False,
    unanswerable: Optional[bool] = None,
    metadata: Optional[dict] = Body(None, embed=True, example={"key": "value"}),
    params: ListAPIParams = Depends(),
):
    ai_obj = await Robot.get_or_none(id=ai_id, deleted_at__isnull=True)
    if not ai_obj:
        raise NotFoundException("AI not found")

    query_params = Q()
    query_params &= Q(deleted_at__isnull=True)
    query_params &= Q(session__robot_id=ai_id)
    # query_params &= Q(status=SessionMessageStatus.FINISHED)

    if not include_test:
        query_params &= Q(session__is_test=False)

    if user_id:
        query_params &= Q(user_id=user_id)
    utc_offset = 0
    if start_time:
        utc_offset = start_time.utcoffset().total_seconds() / 3600
        query_params &= Q(created_at__gte=start_time.astimezone(datetime.timezone.utc))
        query_params &= Q(
            session__created_at__gte=start_time.astimezone(datetime.timezone.utc)
        )
    if end_time:
        utc_offset = end_time.utcoffset().total_seconds() / 3600
        query_params &= Q(created_at__lte=end_time.astimezone(datetime.timezone.utc))
        query_params &= Q(
            session__created_at__lte=end_time.astimezone(datetime.timezone.utc)
        )
    if rating is not None:
        query_params &= Q(rating=rating)
    if only_rating:
        # < 0 or > 0
        query_params &= Q(rating__lt=0) | Q(rating__gt=0)
    if unanswerable == True:
        query_params &= Q(llm_can_answer=False)
    elif unanswerable == False:
        query_params &= Q(llm_can_answer=True)

    distinct_message_ids = set()
    if metadata:
        or_conditions = Q()
        for key, value in metadata.items():
            or_conditions |= Q(key=key, value=value)
        metadatas = await SessionMessageMetadata.filter(
            or_conditions, deleted_at__isnull=True
        )
        # 根据 session_message_id 去重后查询
        distinct_message_ids = set(
            metadata.session_message_id for metadata in metadatas
        )
        # 找到同时满足所有metadata条件的session_message
        for key, value in metadata.items():
            distinct_message_ids &= set(
                metadata.session_message_id
                for metadata in metadatas
                if metadata.key == key and metadata.value == str(value)
            )
    if distinct_message_ids or metadata:
        query_params &= Q(id__in=distinct_message_ids)

    messages = (
        await SessionMessage.filter(query_params)
        .order_by(params.order_by or "-created_at")
        .values(
            "id", "created_at", "rating", "llm_can_answer", "is_correct", "comes_from"
        )
    )
    message_ids = [message.get("id") for message in messages]
    # 查询出关联的 faq，然后以message_id为key，faq_list为value的字典返回
    faq_list = await Faqs.filter(
        session_message_id__in=message_ids, deleted_at__isnull=True
    ).values("session_message_id", "id", "type")
    faq_dict = {}
    for faq in faq_list:
        faq_dict[faq["session_message_id"]] = faq

    # 按天分组
    message_group: dict[str, dict] = {}
    for message in messages:
        created_at = message.get("created_at")
        created_at = created_at + datetime.timedelta(hours=utc_offset)
        day = created_at.strftime("%Y-%m-%d")
        if day not in message_group:
            message_group[day] = {
                "day": day,
                "message_count": 0,
                "evaluation_answer_count": 0,
                "evaluation_source_count": 0,
                "dislike_count": 0,
                "like_count": 0,
                "unanswerable_count": 0,
            }
        message_group[day]["message_count"] += 1
        rating = message.get("rating", 0)
        if rating < 0:
            message_group[day]["dislike_count"] += 1
        elif rating > 0:
            message_group[day]["like_count"] += 1
        message_id = message.get("id")
        if message_id in faq_dict:
            faq = faq_dict[message_id]
            if faq.get("type") == FAQ_TYPE.ANSWER.value:
                message_group[day]["evaluation_answer_count"] += 1
            elif faq.get("type") == FAQ_TYPE.SOURCE.value:
                message_group[day]["evaluation_source_count"] += 1
        llm_can_answer = message.get("llm_can_answer")
        is_correct = message.get("is_correct", False)
        if llm_can_answer is False or rating < 0 or is_correct is True:
            message_group[day]["unanswerable_count"] += 1
    # 把 message_group 转换为 list
    message_group = list(message_group.values())

    return message_group


@router.post(
    "/{ai_id}/session/group_by_day",
    dependencies=[Depends(verify_admin_access)],
)
async def get_session_list_group_by_day(
    ai_id: str,
    start_time: datetime.datetime = None,
    end_time: datetime.datetime = None,
    created_by: Optional[str] = None,
    include_test: bool = False,
    min_unanswerable_count: Optional[int] = None,
    max_unanswerable_count: Optional[int] = None,
    min_score: Optional[float] = None,
    max_score: Optional[float] = None,
    min_correct_count: Optional[int] = None,
    max_correct_count: Optional[int] = None,
    source: Optional[MessagePageInfo] = None,
):
    ai_obj = await Robot.get_or_none(id=ai_id, deleted_at__isnull=True)
    if not ai_obj:
        raise NotFoundException("AI not found")

    session_filter = {
        "robot_id": ai_id,
        "deleted_at__isnull": True,
    }
    utc_offset = 0
    if start_time:
        utc_offset = start_time.utcoffset().total_seconds() / 3600
        session_filter["created_at__gte"] = start_time.astimezone(datetime.timezone.utc)
    if end_time:
        utc_offset = start_time.utcoffset().total_seconds() / 3600
        session_filter["created_at__lte"] = end_time.astimezone(datetime.timezone.utc)
    if not include_test:
        session_filter["is_test"] = False
    if min_unanswerable_count is not None:
        session_filter["session_evaluate__unanswerable_count__gte"] = (
            min_unanswerable_count
        )
    if max_unanswerable_count is not None:
        session_filter["session_evaluate__unanswerable_count__lte"] = (
            max_unanswerable_count
        )
    if min_score is not None:
        session_filter["session_evaluate__score__gte"] = min_score
    if max_score is not None:
        session_filter["session_evaluate__score__lte"] = max_score
    if min_correct_count is not None:
        session_filter["session_evaluate__correct_count__gte"] = min_correct_count
    if max_correct_count is not None:
        session_filter["session_evaluate__correct_count__lte"] = max_correct_count
    if source:
        session_filter["source"] = source.value
    if created_by is not None:
        session_filter["created_by"] = created_by
    session_ids_with_messages = (
        await SessionMessage.all().distinct().values_list("session__id", flat=True)
    )
    sessions = (
        await Session.filter(id__in=session_ids_with_messages, **session_filter)
        .prefetch_related("session_evaluate")
        .order_by("-created_at")
        .values("id", "created_at", "session_evaluate__score")
    )
    # 按创建时间的天数分组
    session_group: dict[str, dict] = {}
    for session in sessions:
        created_at = session.get("created_at")
        created_at = created_at + datetime.timedelta(hours=utc_offset)
        day = created_at.strftime("%Y-%m-%d")
        if day not in session_group:
            session_group[day] = {
                "day": day,
                "session_count": 0,
                "evaluate_score_value": 0,
                "evaluate_score_count": 0,
            }
        session_group[day]["session_count"] += 1
        score = session.get("session_evaluate__score", 0)
        session_group[day]["evaluate_score_value"] += score
        if score > 0:
            session_group[day]["evaluate_score_count"] += 1
    # 把 session_group 转换为 list
    session_group = list(session_group.values())
    return session_group


@router.post(
    "/{ai_id}/session.list",
    response_model=Page[SessionOut],
    dependencies=[Depends(verify_admin_access)],
)
async def get_session_list(
    ai_id: str,
    start_time: Optional[datetime.datetime] = None,
    end_time: Optional[datetime.datetime] = None,
    created_by: Optional[str] = None,
    params: ListAPIParams = Depends(),
    include_test: bool = False,
    min_unanswerable_count: Optional[int] = None,
    max_unanswerable_count: Optional[int] = None,
    min_score: Optional[float] = None,
    max_score: Optional[float] = None,
    min_correct_count: Optional[int] = None,
    max_correct_count: Optional[int] = None,
    source: Optional[MessagePageInfo] = None,
    session_id: Optional[str] = None,
    api_key: ApiKey = Depends(depend_api_key_with_none),
):
    """这个是机器人回答的session列表，一个session代表一次对话"""
    ai_obj = await Robot.get_or_none(id=ai_id, deleted_at__isnull=True)
    if not ai_obj:
        raise NotFoundException("AI not found")

    session_message_queryset = (
        SessionMessage.filter(status=SessionMessageStatus.FINISHED)
        .prefetch_related("session_message_metadata")
        .all()
        .order_by("-created_at")
        if include_test
        else SessionMessage.filter(status=SessionMessageStatus.FINISHED)
        .prefetch_related("session_message_metadata")
        .all()
        .filter(is_test=False)
        .order_by("-created_at")
    )

    session_messages = session_message_queryset

    if start_time is not None:
        session_messages = session_messages.filter(created_at__gte=start_time)
    if end_time is not None:
        session_messages = session_messages.filter(created_at__lte=end_time)

    session_ids = (
        await session_messages.order_by("session_id")
        .distinct()
        .values_list("session_id", flat=True)
    )

    session_filter = {
        "session_id__in": session_ids,
        "robot_id": ai_id,
        "deleted_at__isnull": True,
    }

    if session_id is not None:
        session_filter["session_id"] = session_id

    if not include_test:
        session_filter["is_test"] = False
    if min_unanswerable_count is not None:
        session_filter["session_evaluate__unanswerable_count__gte"] = (
            min_unanswerable_count
        )
    if max_unanswerable_count is not None:
        session_filter["session_evaluate__unanswerable_count__lte"] = (
            max_unanswerable_count
        )
    if min_score is not None:
        session_filter["session_evaluate__score__gte"] = min_score
    if max_score is not None:
        session_filter["session_evaluate__score__lte"] = max_score
    if min_correct_count is not None:
        session_filter["session_evaluate__correct_count__gte"] = min_correct_count
    if max_correct_count is not None:
        session_filter["session_evaluate__correct_count__lte"] = max_correct_count
    if source:
        session_filter["source"] = source.value
    session_filter["session_evaluate__message_count__gt"] = 0
    session = Session.filter(**session_filter)
    if created_by is not None:
        session = session.filter(created_by=created_by)

    queryset = session.prefetch_related(
        Prefetch(
            "sessionmessages",
            queryset=session_message_queryset,
        ),
        "session_evaluate",
    )
    pages = await tortoise_paginate(queryset, params, False)
    # 创建新的分页对象
    filtered_items = await _filter_range_messages(start_time, end_time, pages.items)
    pages = await PaginationUpdater.update_pagination(pages, filtered_items)
    return pages


class PaginationUpdater:
    @staticmethod
    async def update_pagination(pages, filtered_items: list):
        """
        更新现有分页对象的属性
        Args:
            pages: 包含 page, pages, size, total 属性的分页对象
            filtered_items: 过滤后的数据列表
        Returns:
            更新后的分页对象
        """
        new_total = len(filtered_items)
        new_pages = (new_total + pages.size - 1) // pages.size if new_total > 0 else 0

        # 更新相关属性
        pages.total = new_total
        pages.pages = new_pages
        pages.page = min(pages.page, new_pages) if new_pages > 0 else 1
        pages.items = filtered_items

        return pages


async def _filter_range_messages(
    start_time: datetime, end_time: datetime, items: list
) -> list:
    """Filter messages within a range of dates."""
    # 步骤0: 空值检查
    if not items:
        return []

    # 步骤1: 初始化 Pydantic 模型，用于数据序列化
    SessionPydantic = pydantic_model_creator(Session)
    MessagePydantic = pydantic_model_creator(SessionMessage, exclude=("metadata",))

    # 步骤2: 收集所有消息ID，使用集合去重
    message_ids = {
        message.id for session in items for message in session.sessionmessages
    }

    # 步骤3: 批量获取并处理metadata数据
    metadata_map = {}
    if message_ids:
        # 3.1: 根据ID数量选择最优查询方式
        if len(message_ids) == 1:
            message_id = message_ids.pop()
            all_metadata = await SessionMessageMetadata.filter(
                session_message_id=message_id
            ).all()
        else:
            all_metadata = await SessionMessageMetadata.filter(
                session_message_id__in=list(message_ids)
            ).all()

        # 3.2: 构建metadata查找映射
        for metadata in all_metadata:
            metadata_map.setdefault(metadata.session_message_id, []).append(metadata)

    res_items = []
    # 步骤4: 批量转换所有有效session
    session_conversions = [
        SessionPydantic.from_tortoise_orm(session)
        for session in items
        if not (session.title == "" and len(session.sessionmessages) == 0)
    ]
    converted_sessions = await asyncio.gather(*session_conversions)

    # 步骤5: 处理每个session及其消息
    for session, session_pydantic in zip(items, converted_sessions):
        if session.title == "" and len(session.sessionmessages) == 0:
            continue

        session_dict = session_pydantic.dict()
        filtered_messages = []

        # 5.1: 筛选符合时间范围的消息
        valid_messages = sorted(
            [
                message
                for message in session.sessionmessages
                if (not start_time or message.created_at >= start_time)
                and (not end_time or message.created_at <= end_time)
            ],
            key=lambda x: x.created_at,
            reverse=False,
        )

        # 5.2: 批量转换有效消息
        if valid_messages:
            message_conversions = [
                MessagePydantic.from_tortoise_orm(message) for message in valid_messages
            ]
            converted_messages = await asyncio.gather(*message_conversions)
            filtered_messages = [msg.dict() for msg in converted_messages]

        session_dict["sessionmessages"] = filtered_messages

        # 步骤6: 处理session标题
        if not session_dict["title"] and filtered_messages:
            question = filtered_messages[-1]["question"]
            session_dict["title"] = question

        # 步骤7: 处理session评估数据
        if (
            filtered_messages
            and "session_evaluate" in session_dict
            and session_dict["session_evaluate"]
        ):
            actual_message_count = len(filtered_messages)
            actual_unanswerable_count = sum(
                1 for msg in filtered_messages if msg.get("llm_can_answer") is False
            )

            session_dict["session_evaluate"].update(
                {
                    "message_count": actual_message_count,
                    "unanswerable_count": actual_unanswerable_count,
                }
            )
        elif not filtered_messages:
            continue

        res_items.append(session_dict)

    return res_items


@router.post(
    "/{ai_id}/session.user.list",
    response_model=Page[SessionOutExpand],
)
async def get_session_user_list(
    ai_id: str,
    request: Request,
    start_time: Optional[datetime.datetime] = None,
    end_time: Optional[datetime.datetime] = None,
    params: ListAPIParams = Depends(),
    include_test: bool = False,
    min_unanswerable_count: Optional[int] = None,
    max_unanswerable_count: Optional[int] = None,
    min_score: Optional[float] = None,
    max_score: Optional[float] = None,
    source: Optional[MessagePageInfo] = None,
    created_by: Optional[str] = None,
    user: Optional[Auth0User] = Security(get_current_oauth_user_with_gbase_user),
    user_del: Optional[bool] = False,
    api_key: ApiKey = Depends(depend_api_key_with_none),
):
    ai_obj = await Robot.get_or_none(id=ai_id, deleted_at__isnull=True)
    if not ai_obj:
        raise NotFoundException("AI not found")

    created_ip = request.client.host
    if request and "x-real-ip" in request.headers:
        created_ip = request.headers["x-real-ip"]

    if not created_by and user:
        created_by = user.id

    session_message_queryset = (
        SessionMessage.filter(status=SessionMessageStatus.FINISHED)
        .all()
        .prefetch_related("session_message_metadata")
        .order_by("-created_at")
        if include_test
        else SessionMessage.filter(status=SessionMessageStatus.FINISHED)
        .all()
        .prefetch_related("session_message_metadata")
        .filter(is_test=False)
        .order_by("-created_at")
    )
    session_filter = {
        "robot_id": ai_id,
        "deleted_at__isnull": True,
    }
    if not include_test:
        session_filter["is_test"] = False
    if start_time:
        session_filter["created_at__gte"] = start_time
    if end_time:
        session_filter["created_at__lte"] = end_time
    if created_by:
        session_filter["created_by"] = created_by
    elif created_ip:
        session_filter["created_ip"] = created_ip
    else:
        raise InvalidParameterException()
    if min_unanswerable_count is not None:
        session_filter["session_evaluate__unanswerable_count__gte"] = (
            min_unanswerable_count
        )
    if max_unanswerable_count is not None:
        session_filter["session_evaluate__unanswerable_count__lte"] = (
            max_unanswerable_count
        )
    if min_score is not None:
        session_filter["session_evaluate__score__gte"] = min_score
    if max_score is not None:
        session_filter["session_evaluate__score__lte"] = max_score
    if source:
        session_filter["source"] = source.value
    session_filter["session_evaluate__message_count__gt"] = 0
    session = Session.filter(**session_filter)

    if user_del:
        session = session.filter(user_del__not=True)

    user_id = api_key.user_id if api_key else user.id if user else None
    if created_by is None and user_id:
        session_message_queryset = session_message_queryset.filter(user_id=user_id)

    queryset = session.prefetch_related(
        Prefetch(
            "sessionmessages",
            queryset=session_message_queryset,
        ),
        "session_evaluate",
    )
    pages = await tortoise_paginate(queryset, params, False)
    for i in pages.items:
        if i.title == "":
            if len(i.sessionmessages) == 0:
                continue
            question = i.sessionmessages[len(i.sessionmessages) - 1].question
            i.title = question
    return pages


@router.post(
    "/{ai_id}/session.messages.list",
    response_model=Page[SessionMessageExpandWithSessionOut],
    dependencies=[Depends(verify_admin_access)],
)
async def get_session_message_list(
    ai_id: str,
    user_id: Optional[str] = None,
    start_time: datetime.datetime = None,
    end_time: datetime.datetime = None,
    rating: Optional[int] = None,
    only_rating: bool = False,
    unanswerable: Optional[bool] = None,
    metadata: Optional[dict] = Body(None, embed=True, example={"key": "value"}),
    include_test: bool = False,
    params: ListAPIParams = Depends(),
    is_faq: Optional[bool] = None,
    api_key: ApiKey = Depends(depend_api_key_with_none),
):
    ai_obj = await Robot.get_or_none(id=ai_id, deleted_at__isnull=True)
    if not ai_obj:
        raise NotFoundException("AI not found")

    query_params = Q()
    query_params &= Q(deleted_at__isnull=True)
    query_params &= Q(session__robot_id=ai_id)
    query_params &= Q(status=SessionMessageStatus.FINISHED)

    if not include_test:
        query_params &= Q(session__is_test=False)

    if user_id:
        query_params &= Q(user_id=user_id)
    if start_time:
        query_params &= Q(created_at__gte=start_time)
    if end_time:
        query_params &= Q(created_at__lte=end_time)
    if rating is not None:
        query_params &= Q(rating=rating)
    if only_rating:
        # < 0 or > 0
        query_params &= Q(rating__lt=0) | Q(rating__gt=0)
    if unanswerable == True:
        query_params &= Q(llm_can_answer=False)
    elif unanswerable == False:
        query_params &= Q(llm_can_answer=True)
    if is_faq == True:
        query_params &= Q(comes_from=MESSAGE_COMES_FROM.FAQ.value)
    elif is_faq == False:
        query_params &= Q(comes_from__not=MESSAGE_COMES_FROM.FAQ.value)

    distinct_message_ids = set()
    if metadata:
        or_conditions = Q()
        for key, value in metadata.items():
            or_conditions |= Q(key=key, value=value)
        metadatas = await SessionMessageMetadata.filter(
            or_conditions, deleted_at__isnull=True
        )
        # 根据 session_message_id 去重后查询
        distinct_message_ids = set(
            metadata.session_message_id for metadata in metadatas
        )
        # 找到同时满足所有metadata条件的session_message
        for key, value in metadata.items():
            distinct_message_ids &= set(
                metadata.session_message_id
                for metadata in metadatas
                if metadata.key == key and metadata.value == str(value)
            )
    if distinct_message_ids or metadata:
        query_params &= Q(id__in=distinct_message_ids)

    queryset = SessionMessage.filter(query_params).order_by(
        params.order_by or "-created_at"
    )
    pages = await tortoise_paginate(queryset, params, True)
    session_ids = [i.session_id for i in pages.items]
    session_message_queryset = (
        SessionMessage.filter(
            deleted_at__isnull=True, status=SessionMessageStatus.FINISHED
        ).order_by("-created_at")
        if include_test
        else SessionMessage.filter(
            is_test=False, deleted_at__isnull=True, status=SessionMessageStatus.FINISHED
        ).order_by("-created_at")
    )
    sessions = await Session.filter(session_id__in=session_ids).prefetch_related(
        Prefetch(
            "sessionmessages",
            queryset=session_message_queryset,
        )
    )
    session_title_dict = {}
    for session in sessions:
        if session.title == "":
            if len(session.sessionmessages) == 0:
                continue
            question = session.sessionmessages[
                len(session.sessionmessages) - 1
            ].question
            session.title = question
        session_title_dict[session.session_id] = session.title
    for i in pages.items:
        i.session.title = session_title_dict.get(i.session_id, i.session.title)
    return pages


@router.post(
    "/{ai_id}/session.messages.history.list",
    response_model=Page[SessionMessageExpandWithSessionHistoryOut],
    dependencies=[Depends(verify_admin_access)],
)
async def get_session_message_history_list(
    ai_id: str,
    user_id: Optional[str] = None,
    start_time: datetime.datetime = None,
    end_time: datetime.datetime = None,
    language: Optional[str] = None,
    include_test: Optional[bool] = False,
    source: Optional[str] = None,
    feedback_type: Optional[str] = None,
    params: ListAPIParams = Depends(),
    keyword: Optional[str] = None,
    api_key: ApiKey = Depends(depend_api_key_with_none),
):
    """
    对话信息历史列表
    Args:
        ai_id: AI ID
        user_id: 用户ID
        start_time: 开始时间
        end_time: 结束时间
        language: 语言过滤 (例如: "Chinese", "English")
        include_test: 是否包含测试数据
        source: 来源过滤 widget api playground share
        feedback_type: 反馈类型过滤 "good" "bad"
        params: 分页参数
        keyword: 搜索关键词
    """
    ai_obj = await Robot.get_or_none(id=ai_id, deleted_at__isnull=True)
    if not ai_obj:
        raise NotFoundException("AI not found")

    # 如果有feedback_type，先获取符合条件的message_ids
    feedback_message_ids = None
    if feedback_type:
        metadata_query = await SessionMessageMetadata.filter(
            key="type",
            value=feedback_type,
            deleted_at__isnull=True,
            created_at__gte=start_time,
        ).values_list("session_message_id", flat=True)
        feedback_message_ids = set(metadata_query)
        if not feedback_message_ids:  # 如果没有找到匹配的feedback，直接返回空结果
            return Page(items=[], total=0, page=params.page, size=params.size)

    # 构建基础查询条件
    query_params = Q()
    query_params &= Q(deleted_at__isnull=True)
    query_params &= Q(session__robot_id=ai_id)
    query_params &= Q(status=SessionMessageStatus.FINISHED)

    # source过滤
    if source:
        query_params &= Q(session__source=source)

    # feedback_type过滤
    if feedback_message_ids is not None:
        query_params &= Q(id__in=feedback_message_ids)

    # 处理测试数据过滤
    if not include_test:
        # 同时确保session和message都不是测试数据
        query_params &= Q(is_test=False, session__is_test=False)

    # 添加用户ID和时间范围过滤
    if user_id:
        query_params &= Q(user_id=user_id)
    if start_time:
        query_params &= Q(created_at__gte=start_time)
    if end_time:
        query_params &= Q(created_at__lte=end_time)
    # 关键词搜索
    if keyword:
        query_params &= Q(question__icontains=keyword)
    # 语言过滤
    if language:
        query_params &= Q(
            question_metadata__contains={
                "response_language": convert_language_code(language)
            }
        )

    # 使用预加载优化查询
    session_queryset = Session.filter(deleted_at__isnull=True)
    if not include_test:
        # 同时确保session和message都不是测试数据
        session_queryset = session_queryset.filter(is_test=False)
    if source:
        session_queryset = session_queryset.filter(source=source)

    queryset = (
        SessionMessage.filter(query_params)
        .prefetch_related(Prefetch("session", queryset=session_queryset))
        .order_by(params.order_by or "-created_at")
    )

    # 获取分页数据
    pages = await tortoise_paginate(queryset, params, True)

    # 获取message IDs用于获取metadata
    message_ids = [i.id for i in pages.items]

    # 获取metadata数据
    metadata_records = await SessionMessageMetadata.filter(
        session_message_id__in=message_ids, deleted_at__isnull=True
    )

    # 组织metadata数据
    metadata_dict = {}
    for metadata in metadata_records:
        if metadata.session_message_id not in metadata_dict:
            metadata_dict[metadata.session_message_id] = {"type": None, "value": None}
        if metadata.key == "type":
            metadata_dict[metadata.session_message_id]["type"] = metadata.value
        elif metadata.key == "value":
            try:
                metadata_dict[metadata.session_message_id]["value"] = eval(
                    metadata.value
                )
            except:
                metadata_dict[metadata.session_message_id]["value"] = metadata.value

    # 处理返回数据
    result_items = []
    for item in pages.items:
        # 添加feedback信息
        metadata_info = metadata_dict.get(item.id, {})
        item.feedback_type = metadata_info.get("type")
        item.feedback_details = metadata_info.get("value")
        result_items.append(item)

    # 更新分页信息
    pages.items = result_items

    return pages


@router.get(
    "/{ai_id}/session.messages",
    response_model=Page[SessionMessageExpandOut],
    dependencies=[Depends(verify_admin_access)],
)
@api_router.get(
    "/{ai_id}/session.messages",
    response_model=Page[SessionMessageExpandOut],
    tags=["Question"],
)
async def get_session_messages(
    ai_id: str,
    session_id: str,
    end_time: Optional[datetime.datetime] = None,
    params: ListAPIParams = Depends(),
):
    session_obj = await Session.get_or_none(
        robot_id=ai_id, session_id=session_id, deleted_at__isnull=True
    )
    if not session_obj:
        raise NotFoundException("Session not found")
    queryset = SessionMessage.filter(
        session_id=session_id,
        deleted_at__isnull=True,
        status=SessionMessageStatus.FINISHED,
    )
    if end_time:
        queryset = queryset.filter(created_at__lt=end_time)
    queryset = queryset.order_by("-created_at")

    # Return the JSON response
    message_list = await tortoise_paginate(queryset, params, True)
    if len(message_list.items):
        for i in message_list.items:
            if i.reference_list and i.reference_list[0].get("dataset_id"):
                i.default_dataset_id = i.reference_list[0]["dataset_id"]
                i.citation = [
                    {"val": j["source"], "type": "url", "title": j["title"]}
                    for j in i.reference_list
                    if i.reference_indexes
                    and any(t["id"] == j["id"] for t in i.reference_indexes)
                ]

    return message_list


@trace()
async def save_message_question(
    ai_obj: Robot,
    session_id: str,
    user_id: str,
    anonymous_username: str,
    question: str,
    message_id: UUID,
    page_info: Optional[dict] = None,
    created_ip: str = "",
    created_by: str = "",
    is_test: bool = False,
):
    # 记录页面访问信息
    if page_info:
        url = page_info.get("url")
        title = page_info.get("title")
        source = (
            page_info.get("source")
            if page_info.get("source")
            else MessagePageInfo.API.value
        )
    else:
        url = ""
        title = ""
        source = MessagePageInfo.API.value
    # create session if not exist
    session_obj = await Session.get_or_none(session_id=session_id, robot_id=ai_obj.id)
    if not session_obj:
        # 判断是否是新用户
        is_new_user_flag = False
        if created_by:
            is_new_user_flag = await Session.filter(
                robot_id=ai_obj.id, created_by=created_by
            ).exists()
        elif created_ip:
            is_new_user_flag = await Session.filter(
                robot_id=ai_obj.id, created_ip=created_ip
            ).exists()
        else:
            is_new_user_flag = True

        users = [created_by if created_by else created_ip]

        new_users = users if is_new_user_flag else []
        old_users = [] if is_new_user_flag else users

        session_obj = await Session.create(
            session_id=session_id,
            robot=ai_obj,
            title="",
            created_by=created_by,
            created_ip=created_ip,
            is_test=is_test,
            source=source,
        )
        if not is_test:
            # 统计创建session的用户数
            asyncio.create_task(
                create_or_update_session_user_count(
                    ai_obj.id, new_users=new_users, old_users=old_users
                )
            )
    # add to sessionuser
    await SessionUser.add_if_not_exist(session_id, user_id, anonymous_username)

    # add to sessionmessage
    session_message_exist = await SessionMessage.exists(
        id=message_id,
    )
    if session_message_exist:
        raise OperationFailedException("Message already exists")

    question_obj = await session_obj.add_message(
        user_id,
        anonymous_username,
        question,
        message_id,
        is_test=is_test,
        title=title,
        url=url,
    )

    # 统计session的消息数
    tasks = [
        create_session_message_page_access_metrics(ai_obj.id, url, title, 1, source)
    ]
    if not is_test:
        tasks.append(create_or_update_session_message_count(ai_obj.id, 1))
    asyncio.gather(*tasks)

    return question_obj


@trace()
async def save_session_title(session_id, question_record_obj):
    condition = {"session_id": session_id, "deleted_at__isnull": True}
    if session_id:
        await Session.filter(**condition).update(updated_at=datetime.datetime.now())

    session_info = (
        await Session.filter(**condition).prefetch_related("session_evaluate").first()
    )
    if not session_info or session_info.title != "":
        return
    message_count = session_info.session_evaluate.message_count
    if message_count < 5:
        return

    session_message_list = (
        await SessionMessage.filter(**condition).order_by("created_at").limit(10).all()
    )
    dialogue = "\n".join(
        [
            f"User: {message.question}\nAI: {message.answer}\n"
            for message in session_message_list
        ]
    )

    messages = [
        SystemMessage(
            content=session_message_title_sys_template.format(
                chat_log=dialogue,
            )
        ),
        HumanMessage(content=session_message_title_user_template),
    ]
    title_message_id = f"title_{question_record_obj.id}_{uuid.uuid4()}"
    try:
        resp = await chat_acreate(
            origin_question=question_record_obj.question,
            messages=messages,
            max_tokens=1000,
            message_id=title_message_id,
            stream=False,
        )
    except Exception as e:
        logging.error(f"fail to get session title {e}")
        return

    if len(resp.content) > 100:
        resp.content = resp.content[:100]
    session_info.title = resp.content

    await session_info.save()


@trace()
async def answer_ask_about_bot(
    ai_obj: Robot,
    question: QuestionIn,
    event: asyncio.Event,
    response_language: str | None,
):
    model = ai_obj.get_config(AIConfigType.OPENAI_MODEL)
    chatgpt_mode = ai_obj.get_config(AIConfigType.FALLBACK_TO_CHATGPT).lower() == "true"
    if not model:
        model = "gpt_4o"
    if settings.IS_USE_LOCAL_VLLM:
        model = settings.LOCAL_VLLM_MODEL
    time1 = time.monotonic()
    history = (
        await SessionMessage.filter(session_id=question.session_id)
        .order_by("-created_at")
        .limit(3)
    )
    logging.info(
        f"history len: {len(history)}, {history}, time: {time.monotonic() - time1}"
    )

    # Remove the first item(current question), and reverse the order
    history = history[1:][::-1]

    description_text = (
        f'The business scope of the company is "{ai_obj.discrible}". '
        if ai_obj.discrible
        else ""
    )

    what_can_you_do_str = (
        f"I can answer questions about based on my knowledge base, if I can not find the answer in the knowledge base, I will use my own knowledge to help you. "
        if chatgpt_mode
        else f"I can answer your questions about solely based on my knowledge base."
    )

    description_text += f"""
If user asks like "what can you do?", you should say literally:
"{what_can_you_do_str}"
"""

    messages = [
        SystemMessage(
            content=ANSWER_ASK_ABOUT_BOT_SYSTEM_PROMPT.format(
                bot_name=ai_obj.name,
                subject_name=ai_obj.display_subject_name,
                description_text=description_text,
                model=model,
            )
        )
    ]

    # Append history messages
    for session in history:
        messages.append(HumanMessage(content=session.question or ""))
        messages.append(AIMessage(content=session.answer or ""))
    question_text = f"{question.question}"
    if response_language:
        question_text = f"{question_text}\n\n(Please respond in {response_language})"
    messages.append(HumanMessage(content=question_text))
    logging.info(
        f"answer_ask_about_bot messages: {messages}, question: {question}, response_language: {response_language}"
    )
    resp = await chat_acreate(
        origin_question=question.question,
        messages=messages,
        max_tokens=question.max_tokens,
        session_id=question.session_id,
        message_id=question.message_id,
        stream=question.stream,
        format=question.format,
        event=event,
        user_intent=UserIntent.ASK_INFO_ABOUT_BOT,
    )

    if question.stream:
        return StreamingResponse(
            resp,
            media_type="text/event-stream",
        )
    elif question.debug_with_messages:
        return QuestionOut(answer=resp.content, messages=messages)
    else:
        return QuestionOut(answer=resp.content)


@api_router.patch(
    "/{ai_id}/message_rating",
    tags=["Question"],
    status_code=HTTP_204_NO_CONTENT,
)
async def message_rating(
    ai_id: str,
    session_id: str,
    message_id: str,
    rating: int,
    feedback_content: Optional[str] = Body(default=None, embed=True),
    feedback_metadata: Optional[dict] = Body(default={}, embed=True),
    user: Optional[Auth0User] = Security(get_current_oauth_user_with_gbase_user),
    api_key: ApiKey = Depends(depend_api_key_with_none),
):
    session_obj = await Session.get_or_none(
        robot_id=ai_id, session_id=session_id, deleted_at__isnull=True
    )
    if not session_obj:
        raise NotFoundException("Session not found")
    session_message_obj = await SessionMessage.get_or_none(
        session_id=session_id, id=message_id, deleted_at__isnull=True
    )
    if not session_message_obj:
        raise NotFoundException("SessionMessage not found")

    session_message_obj.rating = rating
    session_message_obj.feedback_content = feedback_content or ""

    # await SessionMessageMetadata.filter(session_message=session_message_obj).delete()
    # 获取现有的metadata记录并转换为字典
    existing_metadata = await SessionMessageMetadata.filter(
        session_message=session_message_obj
    ).all()
    metadata_dict = {metadata.key: metadata for metadata in existing_metadata}

    # 更新或创建metadata记录
    for key, value in feedback_metadata.items():
        if key in metadata_dict:
            # 更新现有记录
            metadata_obj = metadata_dict[key]
            metadata_obj.value = value
            # 更新updated_at
            await metadata_obj.save()
        else:
            # 创建新记录
            await SessionMessageMetadata.create(
                session_message=session_message_obj, key=key, value=value
            )
    # for key, value in feedback_metadata.items():
    #     await SessionMessageMetadata.create(
    #         session_message=session_message_obj, key=key, value=value
    #     )
    await session_message_obj.save()
    asyncio.create_task(
        create_or_update_session_rating_count(
            ai_id,
            like_count=1 if rating > 0 else 0,
            dislike_count=1 if rating < 0 else 0,
        )
    )
    return session_message_obj


@api_router.get(
    "/{ai_id}/session.messages/{message_id}",
    tags=["Question Message"],
)
async def get_message(
    ai_id: str,
    message_id: str,
    user: Optional[Auth0User] = Security(get_current_oauth_user_with_gbase_user),
    api_key: ApiKey = Depends(depend_api_key_with_none),
):
    user_id = api_key.user_id if api_key else user.id if user else None
    if api_key and api_key.is_super_admin:
        user_id = None
    elif not user_id:
        raise UnauthorizedException("Unauthorized")

    _filter = {
        "id": message_id,
        "deleted_at__isnull": True,
    }
    if user_id:
        _filter["user_id"] = user_id
    session_message_obj = await SessionMessage.get_or_none(
        id=message_id, deleted_at__isnull=True
    )
    if not session_message_obj:
        raise NotFoundException("SessionMessage not found")

    return session_message_obj


async def get_inside_user_with_api_key(api_key_value):
    try:
        # First, get the user_id based on the api_key_value
        api_key = await Apikey.get(api_key=api_key_value)
        user_id = api_key.user_id

        # Second, get the InsiderPreviewUser based on the user_id
        insider_preview_user = (
            await InsiderPreviewUser.filter(user_id=user_id)
            .prefetch_related("user")
            .get()
        )

        return insider_preview_user
    except DoesNotExist:
        return None


async def api_key_validator(authorization: str = Header(None)):
    if authorization is None:
        raise UnauthorizedException("Authorization header is missing")

    scheme, _, token = authorization.partition(" ")

    if scheme.lower() != "bearer":
        raise UnauthorizedException("Authorization scheme is not Bearer")

    user = await get_inside_user_with_api_key(token)
    if user is None:
        raise UnauthorizedException("Invalid API key")

    return user


class TriggerTaskBody(BaseModel):
    env: str
    send_message_to_lark: bool = True


@router.post("/test/timed-excel-test/trigger")
async def trigger_task(
    body: TriggerTaskBody,
    user: User = Depends(api_key_validator),
):
    env = body.env
    send_message_to_lark = body.send_message_to_lark
    task = test_timed_excel_files.apply_async(
        kwargs={"env": env, "send_message_to_lark": send_message_to_lark}
    )
    if send_message_to_lark:
        message_url = "https://open.larksuite.com/open-apis/bot/v2/hook/380feb82-4743-49f7-b877-68d60eb5494b"
        headers = {"Content-Type": "application/json"}
        text_content = f"""{user.user.name} 触发测试\n环境：{env}\n任务id：{task.id}"""
        text_data = {"msg_type": "text", "content": {"text": text_content}}

        async def send_request(url, headers, data):
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url, headers=headers, data=json.dumps(data)
                ) as response:
                    print(await response.text())

        await send_request(message_url, headers, text_data)
    return {"task_id": task.id, "status": "Task triggered"}


@api_router.post(
    "/faq_recommend_translation/{ai_id}",
    tags=["Question"],
    dependencies=[Depends(verify_questions)],
)
async def ask_recommend_translation(
    recommend: RecommendIn,
    ai_id: UUID,
    user: Optional[Auth0User] = Security(get_current_oauth_user_with_gbase_user),
    api_key: ApiKey = Depends(depend_api_key_with_none),
):
    return await recommend_translation(recommend, ai_id)


@router.get(
    "/{ai_id}/faq_detailed/{faq_id}", dependencies=[Depends(verify_admin_access)]
)
async def faq_detailed(faq_id: UUID):
    faqs_obj = await Faqs.get_or_none(id=faq_id, deleted_at__isnull=True)
    if not faqs_obj:
        raise NotFoundException("FAQs not found")
    return faqs_obj


@router.post(
    "/{ai_id}/question_recommended",
    dependencies=[
        Depends(verify_admin_access),
        Depends(verify_questions),
    ],
)
async def recommended(
    ai_id: UUID,
    recommended_input: RecommendedOut,
    user: Optional[Auth0User] = Security(get_current_oauth_user_with_gbase_user),
    api_key: ApiKey = Depends(depend_api_key_with_none),
):
    user_intent_info = await user_intent(recommended_input.question, "", str(ai_id))
    if user_intent_info == UserIntent.ASK_INFO_ABOUT_BOT.value:
        await QuestionRecommended.create_record(
            ai_id, recommended_input.question, {}, recommended_input.message_id
        )
        return {"recommended_questions": []}

    messages = [
        SystemMessage(
            content=question_recommended_sys_template.format(
                question=recommended_input.question,
            )
        ),
        HumanMessage(content=question_recommended_user_template),
    ]
    if recommended_input.stream is False:
        resp = await chat_acreate(
            origin_question=recommended_input.question,
            messages=messages,
            max_tokens=1000,
            stream=recommended_input.stream,
        )
        json_dict = json.loads(resp.content)
        json_clean_string = json.dumps(
            json_dict,
            ensure_ascii=False,
        ).replace("\n", "")

        await QuestionRecommended.create_record(
            ai_id, recommended_input.question, json_dict, recommended_input.message_id
        )
        return json.loads(json_clean_string)
    else:

        async def ask():
            contents = ""
            contents_simplify = ""
            async for chunk_msg in await chat_acreate(
                origin_question=recommended_input.question,
                messages=messages,
                max_tokens=1000,
                stream=recommended_input.stream,
            ):
                contents += chunk_msg

                yield str({"content": chunk_msg}) + "\n"

            await QuestionRecommended.create_record(
                ai_id,
                recommended_input.question,
                json.loads(contents),
                recommended_input.message_id,
            )

        return StreamingResponse(
            ask(),
            media_type="text/event-stream",
        )


@router.delete(
    "/{ai_id}/session.messages",
    dependencies=[Depends(verify_admin_access)],
    status_code=HTTP_204_NO_CONTENT,
)
@api_router.delete(
    "/{ai_id}/session.messages",
    tags=["Question"],
    status_code=HTTP_204_NO_CONTENT,
)
async def delete_session_messages(
    ai_id: str,
    session_id: str,
    created_by: str = None,
    api_key: ApiKey = Depends(depend_api_key_with_none),
):
    session_obj = await Session.get_or_none(
        robot_id=ai_id,
        session_id=session_id,
        deleted_at__isnull=True,
        created_by=created_by,
    )
    if not session_obj:
        raise HTTPException(status_code=404, detail="Session not found")
    await Session.filter(session_id=session_id).update(user_del=True)
    return


@router.get(
    "/{ai_id}/search",
    response_model=list[SearchResult],
)
async def search(ai_id: str, query: str):
    # 对query进行停用词过滤
    filtered_query = filter_stopwords(query)
    client = OpenSearchFaqClient.get_instance()
    if not client:
        return []
    datasets = await Dataset.filter(robots__id=ai_id, deleted_at__isnull=True)
    dataset_ids = [dataset.id for dataset in datasets]
    if not dataset_ids:
        return []
    rs = await client.asearch(dataset_ids, filtered_query, 0.8, 3)
    if not rs:
        return []

    res = [SearchResult.build_from_hit(hit) for hit in rs["hits"]["hits"]]
    return res


@router.post(
    "/{ai_id}/faqs/list",
)
async def faq_list(
    ids: list[str],
):
    if len(ids) == 0:
        raise NotFoundException("FAQs not found")
    faqs = await Faqs.filter(id__in=ids, deleted_at__isnull=True).values(
        "id", "question", "dataset_id"
    )
    return faqs


# 消息转人工
@router.post(
    "/{ai_id}/message_to_human/{message_id}",
    tags=["Question"],
    status_code=HTTP_204_NO_CONTENT,
)
async def to_human(
    ai_id: str,
    message_id: str,
):
    ai_info = await Robot.get_or_none(id=ai_id, deleted_at__isnull=True)
    if not ai_info:
        raise NotFoundException("AI not found")

    # 消息体上加入转人工标记
    session_message_obj = await SessionMessage.get_or_none(
        id=message_id, deleted_at__isnull=True
    )
    if session_message_obj.transfer_to_human:
        return

    # session加入转人工标记
    if session_message_obj.session_id:
        await Session.filter(session_id=session_message_obj.session_id).update(
            transfer_to_human=True
        )

    if not session_message_obj:
        raise NotFoundException("SessionMessage not found")
    session_message_obj.transfer_to_human = True
    await session_message_obj.save()

    # 判断是否有邮件通知
    asyncio.create_task(send_human_agent_notification_email(ai_id, message_id))


# 参考 message rating 接口，实现设置消息的 Canvas 链接
@api_router.patch(
    "/{ai_id}/message_canvas",
    tags=["Question"],
    status_code=HTTP_204_NO_CONTENT,
)
async def set_message_canvas_url(
    ai_id: str,
    session_id: str,
    message_id: str,
    url: str = Body(..., embed=True),
    title: str = Body(..., embed=True),
):
    session_obj = await Session.get_or_none(
        robot_id=ai_id, session_id=session_id, deleted_at__isnull=True
    )
    if not session_obj:
        raise NotFoundException("Session not found")
    session_message_obj = await SessionMessage.get_or_none(
        session_id=session_id, id=message_id, deleted_at__isnull=True
    )
    if not session_message_obj:
        raise NotFoundException("SessionMessage not found")

    session_message_obj.canvas = {
        "url": url,
        "title": title,
    }
    await session_message_obj.save()


@api_router.get("/stt_token")
def get_azure_stt_token():
    """
    获取 Azure Speech-to-Text Token

    :param subscription_key: Azure Speech 服务的订阅密钥
    :param region: 服务所在区域（例如：eastus, westeurope）
    :return: 访问令牌（JWT Token）
    """
    # 构造请求的 URL
    url = f"https://japaneast.api.cognitive.microsoft.com/sts/v1.0/issueToken"

    # 请求头，包含订阅密钥
    headers = {"Ocp-Apim-Subscription-Key": AZURE_STT_TOKEN_SUBSCRIPTION_KEY}

    try:
        # 发送 POST 请求以获取 Token
        response = requests.post(url, headers=headers)
        response.raise_for_status()  # 检查是否返回 HTTP 错误
        # 返回获取到的 Token
        return response.text
    except requests.exceptions.RequestException as e:
        print(f"获取 Azure STT Token 时发生错误: {e}")
        return e.response


@router.post("/debug/chat", response_model=QuestionOut)
async def debug_chat(
    question: QuestionAgent,  # 使用QuestionAgent作为请求体
    request: Request,
):
    # 1. 参数处理
    messages = question.params.get("messages", [])
    system = question.params.get("system", "")
    tools = question.params.get("tools", [])

    base_model = OpenAIModel.to_model(question.base_model)

    # 2. 构建完整的messages
    if system:
        messages.insert(0, {"role": "system", "content": system})
    # 将messages转换为langchain可用的格式
    # messages = convert_to_messages(messages)
    # 3. 调用底层方法
    response = await question_answer_with_function_call(
        messages=messages,
        functions=tools,
        function_call="auto",
        max_tokens=4096,  # 可以从配置中读取
        stream=False,
        model_name=base_model,
        callbacks=None,  # 如果需要stream，这里需要添加callback
        function_call_style=FunctionCallStyle.OPENAI_STYLE,
    )
    # 4. 返回结果
    return QuestionOut(
        answer=response.content,
        messages=messages,
        tool_calls=response.tool_calls,
    )


@router.post(
    "/debug/prompt/{ai_id}",
)
async def debug_prompt(
    ai_id: str,
    question_agent: QuestionAgent,
    ai_obj: Robot = Depends(verify_questions),
    # user: Optional[Auth0User] = Security(get_current_user),
    api_key: ApiKey = Depends(depend_api_key_with_none),
):
    """
    获取Agent最终prompt的接口, 返回的数据包含最终的system prompt, messages信息, tools信息
    """
    try:
        # 获取历史记录
        if question_agent.params and "messages" in question_agent.params:
            # 如果提供了params且包含messages,使用params中的历史记录
            history = convert_to_messages(question_agent.params["messages"])
        else:
            # 否则从session中获取历史记录
            history, _ = await get_chat_history_turbo(question_agent.session_id, 20)
            history = convert_to_messages(history)
        # question_record_obj = await save_message_question(
        #     ai_obj=ai_obj,
        #     session_id=question_agent.session_id,
        #     user_id=str(user.id) if user else "",
        #     anonymous_username="",
        #     question=question_agent.question,
        #     message_id=uuid.uuid4(),
        # )
        question_record_obj = SessionMessage(
            session_id=question_agent.session_id,
            user_id="google-oauth2|104002370439909913399",
            anonymous_username="",
            question=question_agent.question,
        )
        # 3. 构建QuestionIn对象
        question_in = QuestionIn(
            question=question_agent.question,
            session_id=question_agent.session_id,
            message_id=uuid.uuid4(),
            use_faq=False,
            stream=False,
            with_images=False,
        )
        event = asyncio.Event()
        history, count = await get_chat_history_turbo(question_in.session_id, 20)
        langchain_messages = convert_to_messages(history)
        stream_callback = DigitalHumanFCAgentStreamCallbackHandler(
            question_in=question_in,
            robot=ai_obj,
            format=StreamingMessageDataFormat.OPENAI_AST,
            event=event,
        )
        storage_callback = FCAgentBaseCallbackHandler(
            question_in=question_in,
            robot=ai_obj,
            history=langchain_messages,
            question_record_obj=question_record_obj,
            event=event,
            store_message=True,
        )
        logging.info(f"【chat_agent_runtime】storage_callback: {storage_callback}")
        # 4. 创建AgentFunctionCallEngine实例
        agent_mode = question_agent.agent_mode or "tts"
        afc_engine = AgentFunctionCallEngine(
            question_in=question_in,
            robot=ai_obj,
            chat_history=langchain_messages,
            base_model=OpenAIModel.GPT_4_OMNI,
            stream_callback=stream_callback,
            storage_callback=storage_callback,
            stream=True,
            mode=agent_mode,
            extra_context=question_agent.extra_context,
        )
        # 5. 获取最终prompt
        meta_prompt, openai_history, tools = await afc_engine.get_debug_prompt()
        return {
            "status": "success",
            "data": {
                "meta_prompt": meta_prompt,
                "openai_history": openai_history,
                "tools": tools,
            },
        }
    except Exception as e:
        logging.error(f"Error in get_prompt_debug: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error getting debug prompt: {str(e)}"
        )
