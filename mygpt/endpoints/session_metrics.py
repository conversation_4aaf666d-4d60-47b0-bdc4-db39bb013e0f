import asyncio
import io
import json
import statistics
import uuid
from asyncio import gather
from collections import Counter
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Set
from uuid import UUID

import aiohttp
import pandas as pd
from fastapi import APIRouter, Depends, Response
from fastapi_pagination import Page
from fastapi_pagination.api import create_page
from fastapi_pagination.utils import verify_params
from tortoise.functions import Sum, Count
from tortoise.queryset import Q

from mygpt.authorization import depend_api_key_with_none
from mygpt.enums import (
    MessagePageInfo,
    MESSAGE_COMES_FROM,
    VectorFileStatus,
    VectorFileType,
    FAQ_TYPE,
)
from mygpt.error import InvalidParameterException, NotFoundException
from mygpt.models import (
    SessionMessagePageAccessMetrics,
    SignatureStatistics,
    SessionMessage,
    Robot,
    QuestionRecommended,
    VectorFile,
    RobotAccessStatistics,
    Faqs,
)
from mygpt.parameters import ListAPIParams
from mygpt.schemata import (
    SessionMessagePageAccessInfoIn,
    SessionMessagePageAccessInfoOut,
    SessionMessagePageMetricsWithCountOut,
    SignatureStatisticsIn,
    SignatureStatisticsOut,
    ApiKey,
)
from mygpt.session_metrics import create_session_message_page_access_metrics
from mygpt.settings import STATISTICS_KEY
from mygpt.utils import upload_file_to_s3

router = APIRouter(prefix="/session/metrics", tags=["Session Metrics"])


class SessionMetricsListParams(ListAPIParams):
    """
    会话指标查询参数

    支持时间范围过滤的分页参数。
    """

    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None


@router.get(
    "/statistics",
)
async def get_session_statistics(
    start_date: str, end_date: str, key: str, lark_send: bool = False
):
    if key is not None and key == STATISTICS_KEY:
        download_url = await download_statistics(start_date, end_date)
        if lark_send:
            return await send_lark_message(download_url, start_date, end_date)
        return {"message": "success", "data": download_url}
    else:
        return Response(status_code=403, content="Forbidden")


async def download_statistics(start_date: str, end_date: str):
    start_date = datetime.strptime(start_date, "%Y-%m-%d")
    end_date = datetime.strptime(end_date, "%Y-%m-%d")

    # 判断日期范围不能大于31天
    if (end_date - start_date).days > 31:
        return {"message": "The date range cannot exceed 31 days.", "data": []}

    req_list = []
    vector_file_statistics = []
    robot_pv_statistics = []
    delta = timedelta(days=1)
    current_date = start_date
    while current_date <= end_date:
        start_datetime = current_date.replace(hour=0, minute=0, second=0)
        end_datetime = start_datetime.replace(hour=23, minute=59, second=59)
        robot_ids = await SessionMessage.filter(
            created_at__range=(start_datetime, end_datetime), deleted_at__isnull=True
        ).values("session__robot_id")
        robot_ids = [item.get("session__robot_id") for item in robot_ids]
        unique_robot_ids = list(set(robot_ids))
        if len(unique_robot_ids) == 0:
            current_date += delta
            continue
        loop = asyncio.get_event_loop()
        tasks = []
        for robot_id in unique_robot_ids:
            if robot_id is None:
                continue
            # 分各种会话渠道的数据
            url_forms = await (
                SessionMessage.filter(
                    session__robot_id=robot_id,
                    created_at__range=(start_datetime, end_datetime),
                    is_test=False,
                    deleted_at__isnull=True,
                )
                .annotate(count=Count("url"))
                .group_by("url")
                .values("url", "count")
            )

            if len(url_forms) == 0:
                continue

            robot_info = (
                await Robot.filter(id=robot_id)
                .first()
                .values("name", "id", "created_at")
            )
            if robot_info is not None:
                for url_form in url_forms:
                    tasks.append(
                        loop.create_task(
                            get_statistics(robot_info, current_date, url_form)
                        )
                    )

        vector_file_statistics.append(await get_vector_file_statistics(current_date))
        robot_pv_statistics = await get_pv_statistics(start_date, end_date)
        req_list.extend(await gather(*tasks))
        current_date += delta
    # excel download
    if len(req_list) == 0 and len(vector_file_statistics) == 0:
        return {"message": "No data", "data": []}

    # req_list 和 vector_file_statistics 分别生成两个sheet
    df = pd.DataFrame(req_list)
    df_vector = pd.DataFrame(vector_file_statistics)
    robot_pv = pd.DataFrame(robot_pv_statistics)

    output = io.BytesIO()
    with pd.ExcelWriter(output, engine="openpyxl") as writer:
        df.to_excel(writer, sheet_name="robot_session", index=False)
        df_vector.to_excel(writer, sheet_name="vector_file_statistics", index=False)
        robot_pv.to_excel(writer, sheet_name="robot_pv", index=False)

        for sheet_name in writer.sheets:
            worksheet = writer.sheets[sheet_name]
            for col in worksheet.columns:
                max_length = max(len(str(cell.value)) for cell in col)
                adjusted_width = (max_length + 2) * 1.2
                worksheet.column_dimensions[col[0].column_letter].width = adjusted_width

    output.seek(0)
    current_time = datetime.now().strftime("%Y%m%d-%H:%M:%S")
    filename = f'statistical/{start_date.strftime("%Y-%m-%d")}~{end_date.strftime("%Y-%m-%d")}_{current_time}.xlsx'
    output_file_url = await upload_file_to_s3(
        filename,
        output,
        content_type="application/vnd.openxmlformats-officedocument."
        "spreadsheetml.first_sheet",
    )
    return output_file_url


async def send_lark_message(download_url, start_date, end_date):
    message_url = "https://open.larksuite.com/open-apis/bot/v2/hook/868a1e2b-ddcc-4732-ac77-fd180e47892d"
    headers = {"Content-Type": "application/json"}
    text_content = f"""\
    统计结果
        环境：PROD
        日期范围: {start_date} ~ {end_date}
        输出结果文件url: {download_url}
        """
    data = {"msg_type": "text", "content": {"text": text_content}}
    async with aiohttp.ClientSession() as session:
        async with session.post(
            message_url, headers=headers, data=json.dumps(data)
        ) as response:
            print(await response.text())


async def get_statistics(robot_info: dict, date: datetime, url_form: dict):
    start_date = datetime(date.year, date.month, date.day, 0, 0, 0)
    end_date = start_date + timedelta(days=1)

    total_response_time = 0
    total_messages = 0
    source = ""
    response_times = []

    messages = (
        await SessionMessage.filter(
            url=url_form["url"],
            session__robot_id=robot_info["id"],
            created_at__gte=start_date,
            is_test=False,
            created_at__lt=end_date,
            deleted_at__isnull=True,
        )
        .prefetch_related("session")
        .values("first_response_time", "session__source")
    )

    message_count = await SessionMessage.filter(
        url=url_form["url"],
        session__robot_id=robot_info["id"],
        created_at__gte=start_date,
        created_at__lt=end_date,
        is_test=False,
        deleted_at__isnull=True,
    ).count()

    message_answer_null_count = await SessionMessage.filter(
        Q(url=url_form["url"]),
        Q(session__robot_id=robot_info["id"]),
        Q(created_at__gte=start_date),
        Q(created_at__lt=end_date),
        Q(is_test=False),
        Q(deleted_at__isnull=True),
        Q(answer="") | Q(answer__isnull=True),
    ).count()
    message_answer_fq_count = await SessionMessage.filter(
        url=url_form["url"],
        session__robot_id=robot_info["id"],
        created_at__gte=start_date,
        created_at__lt=end_date,
        is_test=False,
        comes_from=MESSAGE_COMES_FROM.FAQ.value,
        deleted_at__isnull=True,
    ).count()

    session_list = await (
        SessionMessage.filter(
            url=url_form["url"],
            session__robot_id=robot_info["id"],
            created_at__gte=start_date,
            created_at__lt=end_date,
            is_test=False,
            deleted_at__isnull=True,
        )
        .group_by("session__id")
        .values("session__id")
    )

    dislikes_count = await SessionMessage.filter(
        url=url_form["url"],
        session__robot_id=robot_info["id"],
        created_at__gte=start_date,
        created_at__lt=end_date,
        is_test=False,
        deleted_at__isnull=True,
        rating=-1,
    ).count()

    likes_count = await SessionMessage.filter(
        url=url_form["url"],
        session__robot_id=robot_info["id"],
        created_at__gte=start_date,
        created_at__lt=end_date,
        is_test=False,
        deleted_at__isnull=True,
        rating=2,
    ).count()

    image_message_count = await SessionMessage.filter(
        url=url_form["url"],
        session__robot_id=robot_info["id"],
        created_at__gte=start_date,
        answer__contains="![]",
        created_at__lt=end_date,
        is_test=False,
        deleted_at__isnull=True,
    ).count()

    answer_condition = (
        Q(answer__icontains="それは私には理解できま")
        | Q(answer__icontains="現在的知識ではその質問に対答是")
        | Q(answer__icontains="我现在的知识无法回答这个问题")
        | Q(answer__icontains="現在的知識では")
        | Q(answer__icontains="Unfortunately")
        | Q(answer__icontains="answer this question based on my current")
        | Q(answer__icontains="不能在我现在的知识基础上回答这个问题")
        | Q(answer__icontains="在现在的知識下")
        | Q(answer__icontains="我现在的知識無法回答這個問題")
        | Q(answer__icontains="我無法提供具體的信息")
    )
    message_unanswerable_count = await SessionMessage.filter(
        Q(llm_can_answer=False) | answer_condition,
        url=url_form["url"],
        session__robot_id=robot_info["id"],
        created_at__gte=start_date,
        created_at__lt=end_date,
        is_test=False,
        deleted_at__isnull=True,
    ).count()

    image_session_list = await (
        SessionMessage.filter(
            url=url_form["url"],
            session__robot_id=robot_info["id"],
            created_at__gte=start_date,
            created_at__lt=end_date,
            is_test=False,
            answer__contains="![]",
            deleted_at__isnull=True,
        )
        .group_by("session__id")
        .values("session__id")
    )

    for message in messages:
        first_char_response_time = message["first_response_time"]
        if first_char_response_time is not None:
            source = message["session__source"]
            total_response_time += first_char_response_time
            total_messages += 1
            response_times.append(first_char_response_time)

    average_response_time = (
        int(total_response_time / total_messages) if total_messages != 0 else 0
    )
    median_response_time = statistics.median(response_times) if response_times else 0

    if source == MessagePageInfo.WIDGET:
        source = "小部件"
    elif source == MessagePageInfo.API:
        source = "APIs"
    elif source == MessagePageInfo.PLAYGROUND:
        source = "Playground"
    else:
        source = "分享窗"

    return {
        "日期": date.strftime("%Y-%m-%d"),
        "创建日期": robot_info["created_at"].strftime("%Y-%m-%d"),
        "Bot ID": robot_info["id"],
        "机器人名称": robot_info["name"],
        "嵌入地址": url_form["url"],
        "会话渠道": source,
        "会话个数": len(session_list),
        "消息条数": message_count,
        "模态会话个数": len(image_session_list),
        "模态消息数": image_message_count,
        "Q&A消息数": message_answer_fq_count,
        "点差消息": dislikes_count,
        "点赞消息": likes_count,
        "无任何内容回复": message_answer_null_count,
        "消息无法回答": message_unanswerable_count,
        "回答通过": message_count
        - message_answer_null_count
        - message_unanswerable_count,
        "通过率": (
            message_count - message_answer_null_count - message_unanswerable_count
        )
        / message_count
        * 100,
        "会话响应时长": average_response_time,
        "响应时长中位值": median_response_time,
    }


async def get_vector_file_statistics(date: datetime):
    start_date = datetime(date.year, date.month, date.day, 0, 0, 0)
    end_date = start_date + timedelta(days=1)
    vector_files = await VectorFile.filter(
        created_at__gte=start_date, created_at__lt=end_date, deleted_at__isnull=True
    )

    faq_count = 0
    file_count = 0
    html_count = 0
    file_success_count = 0
    html_success_count = 0
    exceeded_count = 0
    tokens = 0
    characters = 0
    file_size = 0
    images = 0
    for vector_file in vector_files:
        if vector_file.file_type == VectorFileType.FAQ:
            faq_count += 1
        elif vector_file.file_type == VectorFileType.UPLOAD:
            file_count += 1
            if vector_file.file_status == VectorFileStatus.COMPLETE:
                file_success_count += 1

        else:
            html_count += 1
            if (
                vector_file.file_status == VectorFileStatus.COMPLETE
                or vector_file.file_status == VectorFileStatus.CRAWLED
            ):
                html_success_count += 1
        if vector_file.file_status == VectorFileStatus.Exceeded:
            exceeded_count += 1
        tokens += vector_file.token_count
        characters += vector_file.characters_count
        file_size += vector_file.file_size
        images += len(vector_file.resources) if vector_file.resources else 0

    return {
        "日期": date.strftime("%Y-%m-%d"),
        "FAQ数": faq_count,
        "上传文件数": file_count,
        "上传文件成功数": file_success_count,
        "上传文件失败数": file_count - file_success_count,
        "网页抓取数": html_count,
        "网页抓取成功数": html_success_count,
        "网页抓取失败数": html_count - html_success_count,
        "超出使用额度数": exceeded_count,
        "tokens": tokens,
        "字符数": characters,
        "文件大小": file_size,
        "图片数": images,
    }


async def get_pv_statistics(start_date, end_date):
    req_list = []

    pv_info_list = await (
        RobotAccessStatistics.filter(
            created_at__gte=start_date, created_at__lt=end_date + timedelta(days=1)
        )
        .order_by("created_at")
        .all()
    )

    if len(pv_info_list) > 0:
        for pv_info in pv_info_list:
            robot_info = (
                await Robot.filter(id=pv_info.robot_id)
                .first()
                .values("name", "id", "created_at")
            )
            req_list.append(
                {
                    "PV产生日期": pv_info.date,
                    "机器人创建日期": robot_info["created_at"].strftime("%Y-%m-%d"),
                    "Bot ID": pv_info.robot_id,
                    "机器人名称": robot_info["name"],
                    "嵌入地址": pv_info.url,
                    "PV": pv_info.count,
                }
            )

    return req_list


@router.get("/{robot_id}")
async def get_session_metrics(
    robot_id: UUID, params: SessionMetricsListParams = Depends()
):
    # 获取时间
    start_time = params.start_date
    end_time = params.end_date

    # 获取机器人信息
    ai_obj = await Robot.get_or_none(id=robot_id, deleted_at__isnull=True)
    if not ai_obj:
        raise NotFoundException("AI not found")

    # 拼接参数
    query_params = Q()
    query_params &= Q(deleted_at__isnull=True)
    query_params &= Q(session__robot_id=robot_id)
    # query_params &= Q(status=SessionMessageStatus.FINISHED)
    query_params &= Q(session__is_test=False)
    utc_offset = 0
    if start_time:
        utc_offset = start_time.utcoffset().total_seconds() / 3600
        query_params &= Q(created_at__gte=start_time.astimezone(timezone.utc))
        query_params &= Q(session__created_at__gte=start_time.astimezone(timezone.utc))
    if end_time:
        utc_offset = end_time.utcoffset().total_seconds() / 3600
        query_params &= Q(created_at__lte=end_time.astimezone(timezone.utc))
        query_params &= Q(session__created_at__lte=end_time.astimezone(timezone.utc))
    messages = (
        await SessionMessage.filter(query_params)
        .prefetch_related("session", "user")
        .order_by(params.order_by or "-created_at")
        .values(
            "id",
            "created_at",
            "rating",
            "llm_can_answer",
            "is_correct",
            "comes_from",
            "session_id",
            "session__created_at",
            "user_id",
        )
    )

    message_ids = [message.get("id") for message in messages]
    faq_list = await Faqs.filter(
        session_message_id__in=message_ids, deleted_at__isnull=True
    ).values("session_message_id", "id", "type")
    faq_dict = {faq["session_message_id"]: faq for faq in faq_list}

    users_appearance_tracker: Dict[str, str] = {}
    user_first_seen: Set[str] = set()

    # 按小时分组
    message_group: Dict[str, Dict] = {}
    sessions_seen: Set[str] = set()

    for message in messages:
        created_at = message.get("created_at")
        created_at += timedelta(hours=utc_offset)
        hour = created_at.strftime("%Y-%m-%d %H")
        # 初始化
        if hour not in message_group:
            message_group[hour] = {
                "id": str(uuid.uuid4()),
                "created_at": created_at.isoformat(),
                "updated_at": created_at.isoformat(),
                "hour_of_day": created_at.hour,
                "date": created_at.strftime("%Y-%m-%d"),
                "message_count": 0,
                "like_count": 0,
                "dislike_count": 0,
                "created_session_count": 0,
                "new_users": [],
                "old_users": [],
                "unanswered_count": 0,
            }

        session_id = message.get("session_id")
        if session_id not in sessions_seen:
            sessions_seen.add(session_id)
            message_group[hour]["created_session_count"] += 1

        message_group[hour]["message_count"] += 1
        rating = message.get("rating", 0)
        if rating < 0:
            message_group[hour]["dislike_count"] += 1
        elif rating > 0:
            message_group[hour]["like_count"] += 1

        message_id = message.get("id")
        if message_id in faq_dict:
            faq = faq_dict[message_id]
            if faq.get("type") == FAQ_TYPE.ANSWER.value:
                if "evaluation_answer_count" not in message_group[hour]:
                    message_group[hour]["evaluation_answer_count"] = 0
                else:
                    message_group[hour]["evaluation_answer_count"] += 1
            elif faq.get("type") == FAQ_TYPE.SOURCE.value:
                if "evaluation_source_count" not in message_group[hour]:
                    message_group[hour]["evaluation_source_count"] = 0
                else:
                    message_group[hour]["evaluation_source_count"] += 1

        llm_can_answer = message.get("llm_can_answer")
        is_correct = message.get("is_correct", False)
        if llm_can_answer is False or rating < 0 or is_correct is True:
            message_group[hour]["unanswered_count"] += 1

        user_id = message.get("user_id")
        if user_id:
            if user_id not in users_appearance_tracker:
                message_group[hour]["new_users"].append(user_id)
                users_appearance_tracker[user_id] = hour
                user_first_seen.add(user_id)
            else:
                if (
                    users_appearance_tracker[user_id] != hour
                    and user_id not in user_first_seen
                ):
                    message_group[hour]["old_users"].append(user_id)

    message_group = list(message_group.values())

    return {
        "items": message_group,
        "total": len(message_group),
        "page": 1,
        "pages": 1,
        "size": len(message_group),
    }


@router.get(
    "/{robot_id}/page", response_model=Page[SessionMessagePageMetricsWithCountOut]
)
async def get_session_message_page_access_metrics(
    robot_id: UUID, params: SessionMetricsListParams = Depends()
):
    date_params = Q()
    if params.start_date:
        date_params &= Q(created_at__gte=params.start_date)
    if params.end_date:
        date_params &= Q(created_at__lte=params.end_date)

    total_query_params = Q(page_access_info__robot_id=robot_id)
    total_query_params &= date_params
    total_records = (
        await SessionMessagePageAccessMetrics.annotate(
            total_message_count=Sum("message_count")
        )
        .prefetch_related("page_access_info")
        .filter(total_query_params)
        .values("total_message_count")
    )
    total_message_count = 0
    for record in total_records:
        total_message_count += record.get("total_message_count", 0)

    count_queryset = SessionMessagePageAccessMetrics.annotate(
        record_count=Count("id")
    ).group_by("page_access_info_id")

    query_params = Q(page_access_info__robot_id=robot_id)
    query_params &= date_params
    queryset = (
        SessionMessagePageAccessMetrics.annotate(message_count=Sum("message_count"))
        .group_by(
            "page_access_info_id", "page_access_info__url", "page_access_info__title"
        )
        .prefetch_related("page_access_info")
        .filter(query_params)
    )

    total = len(await count_queryset.values("record_count"))
    params, raw_params = verify_params(params, "limit-offset")
    items = (
        await queryset.offset(raw_params.offset)
        .limit(raw_params.limit)
        .values("page_access_info__url", "page_access_info__title", "message_count")
    )
    pages = create_page(
        [
            SessionMessagePageMetricsWithCountOut(
                **{
                    "message_count": item.get("message_count", 0),
                    "message_proportion": (
                        ((item.get("message_count", 0) / total_message_count) * 100)
                        if total_message_count > 0
                        else 0
                    ),
                    "url": item.get("page_access_info__url"),
                    "title": item.get("page_access_info__title"),
                }
            )
            for item in items
        ],
        total,
        params,
        **({}),
    )

    return pages


@router.post("/{robot_id}/view", response_model=SessionMessagePageAccessInfoOut)
async def create_session_message_page_view(
    robot_id: UUID, info: SessionMessagePageAccessInfoIn
):
    page_access_info = await create_session_message_page_access_metrics(
        robot_id=robot_id,
        url=info.url,
        title=info.title,
        message_count=0,
        source=info.source,
    )

    return await SessionMessagePageAccessInfoOut.from_tortoise_orm(page_access_info)


@router.post("/{robot_id}/click/signature", response_model=SignatureStatisticsOut)
async def create_click_signature(
    robot_id: UUID,
    info: SignatureStatisticsIn,
    api_key: ApiKey = Depends(depend_api_key_with_none),
):
    today = datetime.now().date()
    is_info = await SignatureStatistics.filter(
        robot_id=robot_id,
        session_id=info.session_id,
        icon=info.icon,
        content=info.content,
        created_at__gte=today,
    ).first()
    if is_info:
        is_info.total_count += 1
        await is_info.save()
        return await SignatureStatisticsOut.from_tortoise_orm(is_info)
    else:
        info = await SignatureStatistics.create(
            robot_id=robot_id,
            session_id=info.session_id,
            total_count=1,
            icon=info.icon,
            content=info.content,
        )
        return await SignatureStatisticsOut.from_tortoise_orm(info)


@router.get("/{robot_id}/export/unknown_answers")
async def export_unknown_answers(
    robot_id: UUID,
    start_time: datetime = None,
    end_time: datetime = None,
):
    query_params = Q()
    query_params &= Q(session__robot__id=robot_id)
    query_params &= Q(llm_can_answer=False)
    query_params &= Q(deleted_at__isnull=True)
    query_params &= Q(is_test=False)
    query_params &= Q(faq_id__isnull=True)
    if start_time:
        query_params &= Q(created_at__gte=start_time)
    if end_time:
        query_params &= Q(created_at__lte=end_time)
    messages = (
        await SessionMessage.filter(query_params)
        .order_by("-created_at")
        .values(
            "id",
            "llm_can_answer",
            "session__id",
            "answer",
            "question",
            "faq_id",
            "created_at",
        )
    )
    return messages


@router.get("/{robot_id}/message/day/count")
async def get_message_count_by_day(
    robot_id: UUID,
    page: int = 1,
    size: int = 10,
    params: SessionMetricsListParams = Depends(),
):
    """
    获取机器人每日消息统计数据

    支持时间范围过滤和分页查询。

    参数说明:
    - robot_id: 机器人ID
    - page: 页码，默认1
    - size: 每页大小，默认10
    - start_date: 开始时间 (可选)，推荐格式: 2024-01-01T00:00:00Z
    - end_date: 结束时间 (可选)，推荐格式: 2024-01-31T23:59:59Z

    返回: 包含每日统计数据的分页响应
    """
    # 构建查询条件
    query_params = Q(
        session__robot__id=robot_id,
        deleted_at__isnull=True,
        is_test=False,
        question__isnull=False,
        answer__isnull=False,
        total_tokens__gt=0,
    )

    # 添加时间范围过滤
    if params.start_date:
        query_params &= Q(created_at__gte=params.start_date)
    if params.end_date:
        query_params &= Q(created_at__lte=params.end_date)

    queryset = (
        await SessionMessage.filter(query_params)
        .order_by("-created_at")
        .values("created_at")
    )

    days_and_counts_dict = Counter(
        [item.get("created_at").strftime("%Y-%m-%d") for item in queryset]
    )

    days_and_counts = []
    for k, v in days_and_counts_dict.items():
        ## remove QuestionRecommended
        #     start_datetime = datetime.strptime(k, "%Y-%m-%d")
        #     end_datetime = start_datetime + timedelta(days=1)
        #     consumed = await QuestionRecommended.filter(
        #         robot__id=robot_id,
        #         created_at__gte=start_datetime,
        #         created_at__lt=end_datetime,
        #     ).count()
        entry = {"day": k, "count": v, "consumed": v}
        days_and_counts.append(entry)

    total = len(days_and_counts)
    pages = total // size + (total % size > 0)

    items = days_and_counts[((page - 1) * size) : (page * size)]

    return {"items": items, "page": page, "pages": pages, "size": size, "total": total}


@router.get("/{robot_id}/message/day/info")
async def get_message_info_by_day(robot_id: UUID, day: str):
    try:
        start_datetime = datetime.strptime(day, "%Y-%m-%d")
    except ValueError:
        raise InvalidParameterException()

    end_datetime = start_datetime + timedelta(days=1)

    session_message_list = await (
        SessionMessage.filter(
            session__robot__id=robot_id,
            deleted_at__isnull=True,
            is_test=False,
            created_at__gte=start_datetime,
            created_at__lt=end_datetime,
            question__isnull=False,
            answer__isnull=False,
            total_tokens__gt=0,
        )
        .order_by("created_at")
        .values("id", "created_at", "session_id", "question")
    )

    # if session_message_list:
    #     session_message_list = [
    #         {
    #             **item,
    #             "consumed": await QuestionRecommended.filter(
    #                 message_id=item["id"]
    #             ).count()
    #             + 1,
    #         }
    #         for item in session_message_list
    #     ]

    return session_message_list
