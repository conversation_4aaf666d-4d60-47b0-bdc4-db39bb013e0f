# Session Metrics API 使用示例

## 端点：GET /{robot_id}/message/day/count

### 基本信息
- **路径**: `/session/metrics/{robot_id}/message/day/count`
- **方法**: GET
- **功能**: 获取指定机器人的每日消息统计数据

### 参数说明

#### 路径参数
- `robot_id` (UUID): 机器人的唯一标识符

#### 查询参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `page` | int | 否 | 1 | 页码，从1开始 |
| `size` | int | 否 | 10 | 每页大小 |
| `start_date` | datetime | 否 | None | 开始时间 |
| `end_date` | datetime | 否 | None | 结束时间 |

### 时间格式支持

#### ✅ 推荐格式（带时区）
```bash
# UTC 时区
start_date=2024-01-01T00:00:00Z

# 指定时区偏移
start_date=2024-01-01T00:00:00+08:00  # 北京时间
start_date=2024-01-01T00:00:00-05:00  # 美东时间
```

#### ⚠️ 可用格式（不带时区）
```bash
# ISO 8601 格式
start_date=2024-01-01T00:00:00

# 空格分隔
start_date=2024-01-01%2000:00:00  # URL 编码后的空格

# Unix 时间戳
start_date=1704067200
```

### 使用示例

#### 1. 基本查询（获取所有数据）
```bash
curl 'https://api-dev.gbase.ai/session/metrics/8cecb50e-3a0c-4f47-bca6-72d7902b1ee8/message/day/count' \
  -H 'authorization: Bearer YOUR_TOKEN'
```

#### 2. 分页查询
```bash
curl 'https://api-dev.gbase.ai/session/metrics/8cecb50e-3a0c-4f47-bca6-72d7902b1ee8/message/day/count?page=1&size=1000' \
  -H 'authorization: Bearer YOUR_TOKEN'
```

#### 3. 时间范围查询（推荐格式）
```bash
curl 'https://api-dev.gbase.ai/session/metrics/8cecb50e-3a0c-4f47-bca6-72d7902b1ee8/message/day/count?start_date=2024-01-01T00:00:00Z&end_date=2024-01-31T23:59:59Z&page=1&size=1000' \
  -H 'authorization: Bearer YOUR_TOKEN'
```

#### 4. 指定时区的查询
```bash
# 北京时间查询
curl 'https://api-dev.gbase.ai/session/metrics/8cecb50e-3a0c-4f47-bca6-72d7902b1ee8/message/day/count?start_date=2024-01-01T00:00:00%2B08:00&end_date=2024-01-31T23:59:59%2B08:00' \
  -H 'authorization: Bearer YOUR_TOKEN'
```

#### 5. 不带时区的查询（注意时区问题）
```bash
curl 'https://api-dev.gbase.ai/session/metrics/8cecb50e-3a0c-4f47-bca6-72d7902b1ee8/message/day/count?start_date=2024-01-01T00:00:00&end_date=2024-01-31T23:59:59' \
  -H 'authorization: Bearer YOUR_TOKEN'
```

### 响应示例

#### 成功响应 (200 OK)
```json
{
  "items": [
    {
      "day": "2024-01-15",
      "count": 25,
      "consumed": 25
    },
    {
      "day": "2024-01-14", 
      "count": 18,
      "consumed": 18
    },
    {
      "day": "2024-01-13",
      "count": 32,
      "consumed": 32
    }
  ],
  "page": 1,
  "pages": 1,
  "size": 10,
  "total": 3
}
```

#### 错误响应示例

##### 时间格式错误 (422 Unprocessable Entity)
```json
{
  "detail": [
    {
      "loc": ["query", "start_date"],
      "msg": "invalid datetime format",
      "type": "value_error.datetime"
    }
  ]
}
```

##### 机器人不存在 (404 Not Found)
```json
{
  "detail": "AI not found"
}
```

### 最佳实践

#### 1. 时区处理
```bash
# ✅ 推荐：明确指定时区
start_date=2024-01-01T00:00:00Z

# ⚠️ 注意：不带时区可能导致混乱
start_date=2024-01-01T00:00:00
```

#### 2. 时间范围查询
```bash
# ✅ 完整的一天查询
start_date=2024-01-01T00:00:00Z&end_date=2024-01-01T23:59:59Z

# ✅ 月度查询
start_date=2024-01-01T00:00:00Z&end_date=2024-01-31T23:59:59Z
```

#### 3. 分页处理
```bash
# 获取大量数据时使用较大的 size
size=1000

# 逐页获取所有数据
page=1&size=100
page=2&size=100
# ...
```

### 注意事项

1. **时区一致性**: 建议始终使用带时区的时间格式，避免时区混乱
2. **性能考虑**: 查询大时间范围时，建议使用较大的 `size` 参数减少请求次数
3. **数据过滤**: 该端点只返回符合以下条件的消息统计：
   - 非测试消息 (`is_test=False`)
   - 有问题和答案的消息
   - 有 token 消耗的消息
   - 未删除的消息
4. **认证**: 需要在请求头中包含有效的 Bearer token
